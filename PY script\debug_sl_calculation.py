#!/usr/bin/env python3

import logging
import config

# Nastavenie logovania
logging.basicConfig(
    level=getattr(logging, config.LOG_LEVEL, logging.INFO),
    format=config.LOG_FORMAT,
    datefmt=config.LOG_DATE_FORMAT
)
logger = logging.getLogger(__name__)

def debug_sl_calculation():
    """Debuguje kalkuláciu SL pre MNQ"""
    
    symbol = 'MNQ'
    signal_type = 'LONG'
    entry_price = 21768.75  # Z logu
    exit_price = 21748.5    # Z logu
    
    logger.info(f"=== DEBUG SL KALKULÁCIE PRE {symbol} ===")
    logger.info(f"Entry price: {entry_price}")
    logger.info(f"Exit price: {exit_price}")
    logger.info(f"Actual loss: {entry_price - exit_price:.2f} bodov")
    
    # Konfigurácia z config.py
    tick_size = config.TICK_SIZES.get(symbol)
    sl_pts = getattr(config, f"SL_PTS_{signal_type.upper()}", 70)
    trail_pts = getattr(config, f"TRAIL_PTS_{signal_type.upper()}", 40)
    trail_offset = getattr(config, f"TRAIL_OFFSET_{signal_type.upper()}", 1)
    
    logger.info(f"\n=== KONFIGURÁCIA ===")
    logger.info(f"Tick size: {tick_size}")
    logger.info(f"SL points: {sl_pts}")
    logger.info(f"Trail points: {trail_pts}")
    logger.info(f"Trail offset: {trail_offset}")
    
    # Kalkulácia SL
    sl_price_difference = sl_pts * tick_size
    expected_sl_price = entry_price - sl_price_difference  # Pre LONG
    
    logger.info(f"\n=== KALKULÁCIA SL ===")
    logger.info(f"SL price difference: {sl_pts} × {tick_size} = {sl_price_difference}")
    logger.info(f"Expected SL price: {entry_price} - {sl_price_difference} = {expected_sl_price}")
    
    # Porovnanie
    actual_loss_points = entry_price - exit_price
    expected_loss_points = sl_price_difference
    
    logger.info(f"\n=== POROVNANIE ===")
    logger.info(f"Expected loss: {expected_loss_points:.2f} bodov")
    logger.info(f"Actual loss: {actual_loss_points:.2f} bodov")
    logger.info(f"Rozdiel: {actual_loss_points - expected_loss_points:.2f} bodov")
    
    if abs(actual_loss_points - expected_loss_points) < 1.0:
        logger.info("✓ SL sa spustil správne")
    else:
        logger.warning("✗ SL sa spustil nesprávne!")
        
    # Kalkulácia trail activation
    trail_activation_price_difference = trail_pts * tick_size
    trail_activation_price = entry_price + trail_activation_price_difference  # Pre LONG
    
    logger.info(f"\n=== TRAILING STOP ===")
    logger.info(f"Trail activation difference: {trail_pts} × {tick_size} = {trail_activation_price_difference}")
    logger.info(f"Trail activation price: {entry_price} + {trail_activation_price_difference} = {trail_activation_price}")
    logger.info(f"Trail offset: {trail_offset} × {tick_size} = {trail_offset * tick_size}")
    
    # Analýza prečo sa pozícia uzavrela tak rýchlo
    logger.info(f"\n=== ANALÝZA RÝCHLEHO UZAVRETIA ===")
    logger.info(f"Pozícia sa uzavrela za {exit_price}, čo je {entry_price - exit_price:.2f} bodov pod entry")
    logger.info(f"To je {(entry_price - exit_price) / tick_size:.0f} tickov")
    
    if exit_price <= expected_sl_price:
        logger.info("Pozícia sa uzavrela na alebo pod SL úrovňou - normálne správanie")
    else:
        logger.warning("Pozícia sa uzavrela nad SL úrovňou - možný problém!")

if __name__ == "__main__":
    debug_sl_calculation()
