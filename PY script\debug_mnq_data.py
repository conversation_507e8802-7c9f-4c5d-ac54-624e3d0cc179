#!/usr/bin/env python3

import logging
import pandas as pd
from datetime import datetime, timedelta
import pytz
from ib_insync import IB, Future
import config

# Nastavenie logovania
logging.basicConfig(
    level=getattr(logging, config.LOG_LEVEL, logging.INFO),
    format=config.LOG_FORMAT,
    datefmt=config.LOG_DATE_FORMAT
)
logger = logging.getLogger(__name__)

def debug_mnq_data():
    """Debuguje MNQ dáta pre 29.5.2025 okolo 20:00"""
    
    ib = IB()
    try:
        logger.info("Pripájam sa k IB...")
        ib.connect(config.IB_HOST, config.IB_PORT, clientId=999)
        
        # Vytvoríme MNQ kontrakt
        contract = Future(symbol='MNQ', exchange='CME', currency='USD')
        contract_details = ib.reqContractDetails(contract)
        
        if not contract_details:
            logger.error("Nenašiel sa MNQ kontrakt")
            return
            
        # Vyberieme aktuálny kontrakt
        active_contract = None
        for cd in contract_details:
            if cd.contract.lastTradeDateOrContractMonth.startswith('202506'):
                active_contract = cd.contract
                break
                
        if not active_contract:
            logger.error("Nenašiel sa MNQ kontrakt pre jún 2025")
            return
            
        logger.info(f"Používam kontrakt: {active_contract.localSymbol}")
        
        # Požiadame o hodinové dáta pre 29.5.2025
        end_time = "20250529 21:00:00 US/Central"  # 21:00 CT = 20:00 NY time
        
        logger.info(f"Požadujem hodinové dáta s endDateTime: {end_time}")
        bars = ib.reqHistoricalData(
            active_contract,
            endDateTime=end_time,
            durationStr='1 D',
            barSizeSetting='1 hour',
            whatToShow='ASK',
            useRTH=False,
            formatDate=1
        )
        
        if not bars:
            logger.error("Nepodarilo sa získať dáta")
            return
            
        # Konvertujeme na DataFrame
        df = pd.DataFrame(bars)
        df['date'] = pd.to_datetime(df['date'])
        
        # Lokalizujeme na UTC ak je potrebné
        if df['date'].dt.tz is None:
            df['date'] = df['date'].dt.tz_localize('UTC')
        elif str(df['date'].dt.tz) != 'UTC':
            df['date'] = df['date'].dt.tz_convert('UTC')
            
        logger.info(f"Získané {len(df)} hodinových sviečok")
        logger.info("Posledné 5 sviečok:")
        
        for i, row in df.tail(5).iterrows():
            local_time = row['date'].astimezone(pytz.timezone('Europe/Bratislava'))
            ny_time = row['date'].astimezone(pytz.timezone('US/Eastern'))
            logger.info(f"{row['date'].strftime('%Y-%m-%d %H:%M UTC')} | "
                       f"{local_time.strftime('%H:%M BA')} | "
                       f"{ny_time.strftime('%H:%M NY')} | "
                       f"O={row['open']:.2f} H={row['high']:.2f} L={row['low']:.2f} C={row['close']:.2f}")
        
        # Nájdeme sviečku 19:00-20:00 NY time (1:00-2:00 UTC počas letného času)
        target_start_utc = datetime(2025, 5, 29, 1, 0, 0, tzinfo=pytz.UTC)
        
        signal_bar = None
        for i, row in df.iterrows():
            if row['date'] == target_start_utc:
                signal_bar = row
                break
                
        if signal_bar is not None:
            logger.info(f"\n=== SIGNÁLNA SVIEČKA 19:00-20:00 NY TIME ===")
            logger.info(f"Čas začiatku (UTC): {signal_bar['date']}")
            logger.info(f"OPEN: {signal_bar['open']:.2f}")
            logger.info(f"HIGH: {signal_bar['high']:.2f}")
            logger.info(f"LOW: {signal_bar['low']:.2f}")
            logger.info(f"CLOSE: {signal_bar['close']:.2f}")
            
            # Porovnáme s vašimi dátami
            expected_open = 21587.50
            expected_close = 21766.00
            
            logger.info(f"\n=== POROVNANIE S GRAFOM ===")
            logger.info(f"Graf OPEN: {expected_open:.2f} | Bot OPEN: {signal_bar['open']:.2f} | Rozdiel: {signal_bar['open'] - expected_open:.2f}")
            logger.info(f"Graf CLOSE: {expected_close:.2f} | Bot CLOSE: {signal_bar['close']:.2f} | Rozdiel: {signal_bar['close'] - expected_close:.2f}")
            
            # Skontrolujeme aj Camarilla levely
            logger.info(f"\n=== CAMARILLA LEVELY Z LOGU ===")
            logger.info(f"H4: 21658.49")
            logger.info(f"L4: 21392.01")
            
            # Testujeme LONG podmienky
            h4 = 21658.49
            close_price = signal_bar['close']
            open_price = signal_bar['open']
            
            cond1 = close_price > h4
            cond2 = open_price < h4
            
            logger.info(f"\n=== LONG PODMIENKY ===")
            logger.info(f"C > H4: {close_price:.2f} > {h4:.2f} = {cond1}")
            logger.info(f"O < H4: {open_price:.2f} < {h4:.2f} = {cond2}")
            logger.info(f"LONG signál by mal byť: {cond1 and cond2}")
            
        else:
            logger.warning("Nenašla sa signálna sviečka pre 19:00-20:00 NY time")
            
    except Exception as e:
        logger.error(f"Chyba: {e}", exc_info=True)
    finally:
        if ib.isConnected():
            ib.disconnect()

if __name__ == "__main__":
    debug_mnq_data()
