#!/usr/bin/env python3
# test_signals.py - Diagnostický script pre testovanie signálnych podmienok

import logging

# Nastavenie logovanie
logging.basicConfig(level=logging.INFO, format='%(asctime)s %(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def test_signal_conditions():
    """Testuje signálne podmienky pre MGC na základe dát z logu"""
    
    # Dáta z logu pre MGC o 6:30 UTC (2:30 NY time)
    symbol = "MGC"
    lo = 3296.20  # open
    lc = 3294.20  # close  
    le = 3297.75  # EMA8
    h4_d = 3336.66  # H4 level
    l4_d = 3263.95  # L4 level
    
    logger.info(f"=== Testovanie signálnych podmienok pre {symbol} ===")
    logger.info(f"Dáta: O={lo:.2f}, C={lc:.2f}, EMA8={le:.2f}")
    logger.info(f"Levely: H4={h4_d:.2f}, L4={l4_d:.2f}")
    
    # LONG signálne podmienky
    long_cond1 = lc > h4_d  # close > H4
    long_cond2 = lo < h4_d  # open < H4  
    long_cond3 = lc > le    # close > EMA8
    long_signal_active = long_cond1 and long_cond2 and long_cond3
    
    logger.info(f"\n--- LONG signál ---")
    logger.info(f"Podmienka 1 - C > H4: {lc:.2f} > {h4_d:.2f} = {long_cond1}")
    logger.info(f"Podmienka 2 - O < H4: {lo:.2f} < {h4_d:.2f} = {long_cond2}")
    logger.info(f"Podmienka 3 - C > EMA8: {lc:.2f} > {le:.2f} = {long_cond3}")
    logger.info(f"LONG signál aktívny: {long_signal_active}")
    
    # SHORT signálne podmienky
    short_cond1 = lc < l4_d  # close < L4
    short_cond2 = lo > l4_d  # open > L4
    short_cond3 = lc < le    # close < EMA8
    short_signal_active = short_cond1 and short_cond2 and short_cond3
    
    logger.info(f"\n--- SHORT signál ---")
    logger.info(f"Podmienka 1 - C < L4: {lc:.2f} < {l4_d:.2f} = {short_cond1}")
    logger.info(f"Podmienka 2 - O > L4: {lo:.2f} > {l4_d:.2f} = {short_cond2}")
    logger.info(f"Podmienka 3 - C < EMA8: {lc:.2f} < {le:.2f} = {short_cond3}")
    logger.info(f"SHORT signál aktívny: {short_signal_active}")
    
    # Analýza prečo signály nie sú aktívne
    logger.info(f"\n=== ANALÝZA ===")
    if not long_signal_active:
        logger.info("LONG signál nie je aktívny, pretože:")
        if not long_cond1:
            logger.info(f"  - Close ({lc:.2f}) nie je vyššie ako H4 ({h4_d:.2f})")
        if not long_cond2:
            logger.info(f"  - Open ({lo:.2f}) nie je nižšie ako H4 ({h4_d:.2f})")
        if not long_cond3:
            logger.info(f"  - Close ({lc:.2f}) nie je vyššie ako EMA8 ({le:.2f})")
    
    if not short_signal_active:
        logger.info("SHORT signál nie je aktívny, pretože:")
        if not short_cond1:
            logger.info(f"  - Close ({lc:.2f}) nie je nižšie ako L4 ({l4_d:.2f})")
        if not short_cond2:
            logger.info(f"  - Open ({lo:.2f}) nie je vyššie ako L4 ({l4_d:.2f})")
        if not short_cond3:
            logger.info(f"  - Close ({lc:.2f}) nie je nižšie ako EMA8 ({le:.2f})")
    
    # Aké by mali byť hodnoty pre aktiváciu signálov
    logger.info(f"\n=== ČO BY BOLO POTREBNÉ PRE SIGNÁLY ===")
    logger.info(f"Pre LONG signál by close muselo byť > {h4_d:.2f} (aktuálne {lc:.2f})")
    logger.info(f"Pre SHORT signál by close muselo byť < {l4_d:.2f} (aktuálne {lc:.2f})")
    logger.info(f"Rozdiel od H4: {lc - h4_d:.2f} (potrebné > 0)")
    logger.info(f"Rozdiel od L4: {lc - l4_d:.2f} (potrebné < 0)")

if __name__ == "__main__":
    test_signal_conditions()
