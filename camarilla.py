#!/usr/bin/env python3
# camarilla.py

import logging
import time
from datetime import datetime, timedelta
from ib_insync import IB, util, Order, MarketOrder, Contract
import pandas as pd
import pytz
import sys
import os

import config
import utils
import ib_interface

logger = logging.getLogger(__name__)

ib = IB()
last_closed_conids_session = set()
current_active_expiry = None
instruments_data_list = []
theoretical_new_bar_start_utc = None
needs_data_initialization = True


def initialize_instruments_data():
    global instruments_data_list, current_active_expiry, ib, needs_data_initialization
    logger.info("Inicializujem/Reinicializujem dáta inštrumentov...")

    previous_positions_state = {
        inst_data.get('symbol'): {
            'inPosition': inst_data.get('inPosition', False),
            'entry_signal': inst_data.get('entry_signal', '')
        } for inst_data in instruments_data_list if inst_data.get('symbol')
    }

    temp_instruments_data_list = [] # Použijeme dočasný zoznam

    # NOVÁ LOGIKA: Nepoužívame už current_active_expiry pre všetky nástroje
    # Každý nástroj si vyberie svoj najlepší kontrakt individuálne
    if current_active_expiry is None:
        current_active_expiry = "DYNAMIC"  # Označenie, že používame dynamický výber
        logger.info("Používam dynamický výber najlepších kontraktov pre každý nástroj")

    if not config.INSTRUMENTS_CONFIG:
        logger.warning("Zoznam INSTRUMENTS_CONFIG v config.py je prázdny!")
        instruments_data_list = [] # Uistíme sa, že je prázdny
        needs_data_initialization = False
        return

    for inst_conf in config.INSTRUMENTS_CONFIG:
        inst_data_new = inst_conf.copy()
        inst_data_new.update({
            'contract': None, 'inPosition': False, 'entry_price': 0.0,
            'entry_signal': '', 'entry_time': '', 'entry_timestamp': 0.0,
            'sl_order_id': None, 'sl_price': 0.0, 'trailing_set': False,
            'trail_offset_reached': False, 'trail_activation_price': 0.0,
            'trail_offset': 0.0, 'last_bar_processed_utc': None
        })

        symbol = inst_data_new['symbol']
        try:
            logger.info(f"[{symbol}] Načítavam najlepší dostupný kontrakt...")
            # NOVÁ LOGIKA: Použiť novú funkciu pre výber najlepšieho kontraktu
            inst_data_new['contract'] = utils.get_best_contract_for_instrument(
                ib, symbol, inst_data_new['exchange'], inst_data_new['currency']
            )
            if inst_data_new['contract']:
                logger.info(f"[{symbol}] Kontrakt úspešne načítaný: {getattr(inst_data_new['contract'], 'localSymbol', symbol)}")
                if symbol in previous_positions_state and previous_positions_state[symbol]['inPosition']:
                    logger.warning(f"[{symbol}] Po (re)inicializácii kontraktov bol tento inštrument predtým v pozícii ({previous_positions_state[symbol]['entry_signal']}). "
                                   f"Interný stav 'inPosition' je teraz False. Nutná synchronizácia.")
            else:
                logger.error(f"[{symbol}] Nepodarilo sa načítať kontrakt pri (re)inicializácii.")
        except Exception as e_init_contract_loop:
            logger.error(f"[{symbol}] Chyba pri načítaní kontraktu pri (re)inicializácii: {e_init_contract_loop}", exc_info=True)

        temp_instruments_data_list.append(inst_data_new)

    instruments_data_list = temp_instruments_data_list # Priradíme až po úspešnom naplnení dočasného zoznamu
    valid_contracts_count = sum(1 for i in instruments_data_list if i.get('contract') is not None)
    logger.info(f"Inicializovaných/Reinicializovaných {len(instruments_data_list)} inštrumentov. Platných kontraktov: {valid_contracts_count}")
    needs_data_initialization = False


def safe_process_dataframe_index(df_input, symbol_for_log, data_type_log):
    if df_input is None or df_input.empty:
        logger.warning(f"[{symbol_for_log}] Prijatý prázdny alebo None DataFrame pre {data_type_log} dáta.")
        return None

    df = df_input.copy()

    if df.index.tz is None:
        logger.debug(f"[{symbol_for_log}] Index DataFrame pre {data_type_log} dáta je naivný (bez časovej zóny), lokalizujem na UTC.")
        df.index = pd.to_datetime(df.index, utc=True)
    elif df.index.tz != pytz.utc:
        logger.debug(f"[{symbol_for_log}] Index DataFrame pre {data_type_log} dáta má časovú zónu {df.index.tz}, konvertujem na UTC.")
        df.index = df.index.tz_convert(pytz.utc)

    if df.empty:
        logger.warning(f"[{symbol_for_log}] DataFrame pre {data_type_log} dáta je prázdny po spracovaní indexu.")
        return None

    return df


def get_historical_data_for_instrument(inst_data, duration_str, bar_size, what_to_show, use_rth, end_datetime_utc_str):
    symbol = inst_data['symbol']
    contract = inst_data['contract']

    if not contract:
        logger.error(f"[{symbol}] Kontrakt nie je dostupný pre získanie historických dát.")
        return None

    try:
        logger.info(f"[{symbol}] Požadujem TF dáta s endDateTime (UTC): {end_datetime_utc_str} a duration {duration_str}")

        bars = ib.reqHistoricalData(
            contract,
            endDateTime=end_datetime_utc_str,
            durationStr=duration_str,
            barSizeSetting=bar_size,
            whatToShow=what_to_show,
            useRTH=use_rth,
            formatDate=1
        )

        if not bars:
            logger.warning(f"[{symbol}] Žiadne historické dáta pre TF.")
            return None

        df = util.df(bars)
        if df.empty:
            logger.warning(f"[{symbol}] Prázdny DataFrame z historických dát pre TF.")
            return None

        df_processed = safe_process_dataframe_index(df, symbol, "TF")
        if df_processed is None:
            return None

        logger.info(f"[{symbol}] Získané TF dáta: {len(df_processed)} sviečok od {df_processed.index[0]} do {df_processed.index[-1]}")

        # Log posledných pár sviečok pre debugging
        for i, (timestamp, row) in enumerate(df_processed.tail(3).iterrows()):
            logger.info(f"[{symbol}] TF sviečka {i+1}: {timestamp} | O={row['open']:.2f} H={row['high']:.2f} L={row['low']:.2f} C={row['close']:.2f}")

        return df_processed

    except Exception as e:
        logger.error(f"[{symbol}] Chyba pri získavaní historických TF dát: {e}", exc_info=True)
        return None


def get_daily_data_for_instrument(inst_data, end_datetime_utc_str):
    symbol = inst_data['symbol']
    contract = inst_data['contract']

    if not contract:
        logger.error(f"[{symbol}] Kontrakt nie je dostupný pre získanie denných dát.")
        return None

    try:
        logger.info(f"[{symbol}] Požadujem denné dáta s endDateTime (UTC): {end_datetime_utc_str}")

        daily_bars = ib.reqHistoricalData(
            contract,
            endDateTime=end_datetime_utc_str,
            durationStr='5 D',
            barSizeSetting='1 day',
            whatToShow='ASK',
            useRTH=True,
            formatDate=1
        )

        if not daily_bars:
            logger.warning(f"[{symbol}] Žiadne denné dáta.")
            return None

        df_daily = util.df(daily_bars)
        if df_daily.empty:
            logger.warning(f"[{symbol}] Prázdny DataFrame z denných dát.")
            return None

        df_daily_processed = safe_process_dataframe_index(df_daily, symbol, "denné")
        if df_daily_processed is None:
            return None

        logger.info(f"[{symbol}] Získané denné dáta: {len(df_daily_processed)} dní od {df_daily_processed.index[0]} do {df_daily_processed.index[-1]}")
        return df_daily_processed

    except Exception as e:
        logger.error(f"[{symbol}] Chyba pri získavaní denných dát: {e}", exc_info=True)
        return None


def run_main_trading_loop():
    global last_closed_conids_session, current_active_expiry, instruments_data_list, ib
    global theoretical_new_bar_start_utc, needs_data_initialization

    if needs_data_initialization:
        logger.info("run_main_trading_loop: Potrebná (re)inicializácia dát inštrumentov.")
        initialize_instruments_data()

    active_instruments_to_process = [inst for inst in instruments_data_list if inst.get('contract')]
    if not active_instruments_to_process:
        logger.error("run_main_trading_loop: Žiadne platné inštrumenty s kontraktami na spracovanie. Čakám 60s.")
        needs_data_initialization = True
        time.sleep(60)
        return

    if theoretical_new_bar_start_utc is None:
        logger.info("run_main_trading_loop: Prvá barová iterácia, čakám na prvú celú sviečku...")
        theoretical_new_bar_start_utc = utils.wait_for_next_bar(config.TIMEFRAME)

    timeframe_delta = timedelta(hours=1) if 'hour' in config.TIMEFRAME else timedelta(minutes=int(config.TIMEFRAME.split()[0]))

    expected_signal_bar_start_utc = theoretical_new_bar_start_utc - timeframe_delta
    logger.info(f"Pre tento cyklus je očakávaný začiatok signálnej sviečky (UTC): {expected_signal_bar_start_utc.strftime('%Y-%m-%d %H:%M:%S %Z')}")

    # NOVÁ LOGIKA: Rollover sa už nerobí globálne, ale individuálne pre každý nástroj
    # Kontrola rolloveru sa presunie do samostatnej funkcie, ktorá sa bude volať periodicky
    # Zatiaľ túto logiku vypneme
    logger.debug("Rollover kontrola je teraz vypnutá - používame dynamický výber kontraktov")

    if config.CHECK_EOD_CLOSURE_ENABLED and utils.is_near_market_close():
        logger.info("EOD CHECK: Podmienka is_near_market_close() je TRUE. Kontrolujem pozície na uzavretie.")
        any_position_closed_eod_flag = False

        for inst_data in active_instruments_to_process:
            symbol = inst_data['symbol']
            if inst_data.get('inPosition', False):
                logger.info(f"[{symbol}] EOD: Inštrument je v pozícii, zatváram pozíciu pred koncom dňa.")
                try:
                    close_success = ib_interface.close_position_market_order(ib, inst_data)
                    if close_success:
                        any_position_closed_eod_flag = True
                        logger.info(f"[{symbol}] EOD: Pozícia úspešne zatvorená.")
                    else:
                        logger.error(f"[{symbol}] EOD: Nepodarilo sa zatvoriť pozíciu.")
                except Exception as e_eod_close:
                    logger.error(f"[{symbol}] EOD: Chyba pri zatváraní pozície: {e_eod_close}", exc_info=True)
            else:
                logger.debug(f"[{symbol}] EOD: Inštrument nie je v pozícii, preskakujem.")

        if any_position_closed_eod_flag:
            logger.info("EOD CHECK: Aspoň jedna pozícia bola zatvorená. Čakám na ďalšiu sviečku.")
        else:
            logger.info("EOD CHECK: Žiadne pozície na zatvorenie.")

        theoretical_new_bar_start_utc = utils.wait_for_next_bar(config.TIMEFRAME)
        return

    # Hlavná logika obchodovania
    end_datetime_utc_str = expected_signal_bar_start_utc.strftime('%Y%m%d %H:%M:%S UTC')

    for inst_data in active_instruments_to_process:
        symbol = inst_data['symbol']

        try:
            # Získaj historické dáta
            df_tf = get_historical_data_for_instrument(
                inst_data, '1 D', config.TIMEFRAME, 'ASK', False, end_datetime_utc_str
            )

            if df_tf is None or df_tf.empty:
                logger.warning(f"[{symbol}] Preskakujem kvôli chýbajúcim TF dátam.")
                continue

            df_daily = get_daily_data_for_instrument(inst_data, end_datetime_utc_str)

            if df_daily is None or df_daily.empty:
                logger.warning(f"[{symbol}] Preskakujem kvôli chýbajúcim denným dátam.")
                continue

            # Výpočet Camarilla pivotov
            h4, l4 = utils.calc_pivots(df_daily, symbol)

            if h4 is None or l4 is None:
                logger.warning(f"[{symbol}] Preskakujem kvôli chýbajúcim pivotom.")
                continue

            # Nájdi signálnu sviečku
            signal_bar = None
            for timestamp, row in df_tf.iterrows():
                if timestamp == expected_signal_bar_start_utc:
                    signal_bar = row
                    break

            if signal_bar is None:
                logger.warning(f"[{symbol}] Signálna sviečka pre {expected_signal_bar_start_utc} nebola nájdená.")
                continue

            # Kontrola či už bola táto sviečka spracovaná
            if inst_data.get('last_bar_processed_utc') == expected_signal_bar_start_utc:
                logger.debug(f"[{symbol}] Sviečka {expected_signal_bar_start_utc} už bola spracovaná, preskakujem.")
                continue

            # Označenie sviečky ako spracovanej
            inst_data['last_bar_processed_utc'] = expected_signal_bar_start_utc

            # Analýza signálov
            signal_open = float(signal_bar['open'])
            signal_close = float(signal_bar['close'])

            logger.info(f"[{symbol}] Signálna sviečka: O={signal_open:.4f}, C={signal_close:.4f}, H4={h4:.4f}, L4={l4:.4f}")

            # Logika signálov
            long_signal = signal_open < l4 and signal_close > l4
            short_signal = signal_open > h4 and signal_close < h4

            if inst_data.get('inPosition', False):
                logger.debug(f"[{symbol}] Už som v pozícii ({inst_data.get('entry_signal', 'unknown')}), preskakujem nové signály.")
                continue

            if long_signal:
                logger.info(f"[{symbol}] LONG signál detekovaný!")
                try:
                    success = ib_interface.place_market_order_with_sl_trailing(
                        ib, inst_data, 'BUY', config.QUANTITY,
                        config.SL_PTS_LONG, config.TRAIL_PTS_LONG, config.TRAIL_OFFSET_LONG
                    )
                    if success:
                        inst_data['inPosition'] = True
                        inst_data['entry_signal'] = 'LONG'
                        inst_data['entry_time'] = datetime.now(pytz.utc).strftime('%Y-%m-%d %H:%M:%S UTC')
                        logger.info(f"[{symbol}] LONG pozícia úspešne otvorená.")
                    else:
                        logger.error(f"[{symbol}] Nepodarilo sa otvoriť LONG pozíciu.")
                except Exception as e_long:
                    logger.error(f"[{symbol}] Chyba pri otváraní LONG pozície: {e_long}", exc_info=True)

            elif short_signal:
                logger.info(f"[{symbol}] SHORT signál detekovaný!")
                try:
                    success = ib_interface.place_market_order_with_sl_trailing(
                        ib, inst_data, 'SELL', config.QUANTITY,
                        config.SL_PTS_SHORT, config.TRAIL_PTS_SHORT, config.TRAIL_OFFSET_SHORT
                    )
                    if success:
                        inst_data['inPosition'] = True
                        inst_data['entry_signal'] = 'SHORT'
                        inst_data['entry_time'] = datetime.now(pytz.utc).strftime('%Y-%m-%d %H:%M:%S UTC')
                        logger.info(f"[{symbol}] SHORT pozícia úspešne otvorená.")
                    else:
                        logger.error(f"[{symbol}] Nepodarilo sa otvoriť SHORT pozíciu.")
                except Exception as e_short:
                    logger.error(f"[{symbol}] Chyba pri otváraní SHORT pozície: {e_short}", exc_info=True)

            else:
                logger.debug(f"[{symbol}] Žiadny signál.")

        except Exception as e_instrument:
            logger.error(f"[{symbol}] Chyba pri spracovaní inštrumentu: {e_instrument}", exc_info=True)

    # Čakanie na ďalšiu sviečku
    theoretical_new_bar_start_utc = utils.wait_for_next_bar(config.TIMEFRAME)


if __name__ == '__main__':
    utils.setup_logging()
    logger.info(f"Štartuje Camarilla multi-inštrument bot, TF={config.TIMEFRAME}, PID={os.getpid()}")

    bot_crashed_flag_main_outer = False
    consecutive_crashes_main_outer_loop = 0

    try:
        current_active_expiry = "DYNAMIC"  # Nová logika - dynamický výber kontraktov
        logger.info("Inicializujem s dynamickým výberom najlepších kontraktov")
    except Exception as e_init_expiry_main_outer_loop:
        logger.critical(f"Nepodarilo sa inicializovať: {e_init_expiry_main_outer_loop}. Ukončujem.")
        sys.exit(1)

    while True:
        is_likely_gateway_restarting_flag_outer = False
        try:
            if not ib.isConnected():
                max_attempts_for_connection_local_outer = config.MAX_GATEWAY_RESTART_CONNECT_ATTEMPTS if is_likely_gateway_restarting_flag_outer else config.MAX_CONNECT_ATTEMPTS

                for attempt_idx_main_conn_loop_outer_loop in range(max_attempts_for_connection_local_outer):
                    try:
                        logger.info(f"Pokus o pripojenie č. {attempt_idx_main_conn_loop_outer_loop + 1}/{max_attempts_for_connection_local_outer}...")
                        ib.connect(config.IB_HOST, config.IB_PORT, clientId=config.IB_CLIENT_ID, timeout=20)
                        if ib.isConnected():
                            logger.info("Úspešne pripojený k IB.")
                            if needs_data_initialization or not instruments_data_list or not all(inst.get('contract') for inst in instruments_data_list):
                                logger.info("Volám initialize_instruments_data() z __main__ po pripojení.")
                                initialize_instruments_data()
                            is_likely_gateway_restarting_flag_outer = False
                            break
                    except ConnectionRefusedError as cre_main_inner_loop_outer_loop:
                        logger.warning(f"ConnectionRefusedError pri pokuse {attempt_idx_main_conn_loop_outer_loop + 1}: {cre_main_inner_loop_outer_loop}")
                        is_likely_gateway_restarting_flag_outer = True
                        if attempt_idx_main_conn_loop_outer_loop < max_attempts_for_connection_local_outer - 1:
                            logger.info("Čakám 10s pred ďalším pokusom...")
                            time.sleep(10)
                    except Exception as e_main_inner_loop_outer_loop:
                        logger.error(f"Chyba pri pripojení (pokus {attempt_idx_main_conn_loop_outer_loop + 1}): {e_main_inner_loop_outer_loop}")
                        if attempt_idx_main_conn_loop_outer_loop < max_attempts_for_connection_local_outer - 1:
                            logger.info("Čakám 5s pred ďalším pokusom...")
                            time.sleep(5)

                if not ib.isConnected():
                    logger.critical(f"Nepodarilo sa pripojiť k IB po {max_attempts_for_connection_local_outer} pokusoch. Ukončujem.")
                    sys.exit(1)

            run_main_trading_loop()
            consecutive_crashes_main_outer_loop = 0

        except KeyboardInterrupt:
            logger.info("Prerušené používateľom (Ctrl+C). Ukončujem...")
            break
        except Exception as e_main_outer_loop:
            consecutive_crashes_main_outer_loop += 1
            logger.error(f"Chyba v hlavnej slučke (#{consecutive_crashes_main_outer_loop}): {e_main_outer_loop}", exc_info=True)

            if consecutive_crashes_main_outer_loop >= config.MAX_CONSECUTIVE_CRASHES_LIMIT:
                logger.critical(f"Dosiahnutý limit {config.MAX_CONSECUTIVE_CRASHES_LIMIT} po sebe idúcich pádov. Čakám 300s pred pokračovaním...")
                time.sleep(300)
                consecutive_crashes_main_outer_loop = 0
            else:
                logger.info("Čakám 30s pred reštartom...")
                time.sleep(30)

    if ib.isConnected():
        ib.disconnect()
        logger.info("Odpojený od IB.")

    logger.info("Bot ukončený.")
