# debug_mgc_data.py
import logging
import pandas as pd
import pytz
from datetime import datetime, timedelta
from ib_insync import IB, Future, util
import config

# Nastavenie logovania
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s %(levelname)s:%(name)s:%(lineno)d:%(funcName)s:%(message)s',
    handlers=[
        logging.FileHandler('debug_mgc_data.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def debug_mgc_data():
    """Debuguje MGC dáta pre signálnu sviečku 20:00 NY time 28.5.2025"""

    ib = IB()
    try:
        logger.info("Pripájam sa k IB...")
        ib.connect(config.IB_HOST, config.IB_PORT, clientId=999)

        # Vytvoríme MGC kontrakt
        contract = Future(symbol='MGC', exchange='COMEX', currency='USD')
        contract_details = ib.reqContractDetails(contract)

        if not contract_details:
            logger.error("Nenašiel sa MGC kontrakt")
            return

        # Vyberieme aktuálny kontrakt
        active_contract = None
        for cd in contract_details:
            if cd.contract.lastTradeDateOrContractMonth.startswith('202506'):
                active_contract = cd.contract
                break

        if not active_contract:
            logger.error("Nenašiel sa MGC kontrakt pre jún 2025")
            return

        logger.info(f"Používam kontrakt: {active_contract.localSymbol}")

        # Simulujeme presne tú istú logiku ako v hlavnom skripte
        # Ak bot beží o 2:00:11 UTC, tak theoretical_new_bar_start_utc = 2:00:00 UTC
        # a end_datetime_for_tf_request = 1:59:59 UTC
        theoretical_new_bar_start_utc = datetime(2025, 5, 29, 2, 0, 0, tzinfo=pytz.UTC)
        end_datetime_for_tf_request = theoretical_new_bar_start_utc - timedelta(seconds=1)

        logger.info(f"Simulujem hlavný skript:")
        logger.info(f"theoretical_new_bar_start_utc: {theoretical_new_bar_start_utc}")
        logger.info(f"end_datetime_for_tf_request: {end_datetime_for_tf_request}")

        # Vypočítame expected_signal_bar_start_utc (rovnako ako v hlavnom skripte)
        timeframe_delta = timedelta(hours=1)  # Pre 1 hour timeframe
        expected_signal_bar_start_utc = theoretical_new_bar_start_utc - timeframe_delta

        logger.info(f"expected_signal_bar_start_utc: {expected_signal_bar_start_utc}")

        # Konvertujeme na formát pre IB API
        end_time = end_datetime_for_tf_request.strftime('%Y%m%d %H:%M:%S %Z')

        logger.info(f"Požadujem hodinové dáta s endDateTime: {end_time}")
        bars = ib.reqHistoricalData(
            active_contract,
            endDateTime=end_time,
            durationStr='1 D',
            barSizeSetting='1 hour',
            whatToShow='ASK',
            useRTH=False,
            formatDate=1
        )

        if not bars:
            logger.error("Nepodarilo sa získať dáta")
            return

        # Konvertujeme na DataFrame
        df = pd.DataFrame(bars)
        df['date'] = pd.to_datetime(df['date'])

        # Lokalizujeme na UTC ak je potrebné
        if df['date'].dt.tz is None:
            df['date'] = df['date'].dt.tz_localize('UTC')
        elif str(df['date'].dt.tz) != 'UTC':
            df['date'] = df['date'].dt.tz_convert('UTC')

        # Nastavíme date ako index (rovnako ako v hlavnom skripte)
        df = df.set_index('date')

        logger.info(f"Získané {len(df)} hodinových sviečok")
        logger.info("Posledné 5 sviečok:")

        for date_index, row in df.tail(5).iterrows():
            local_time = date_index.astimezone(pytz.timezone('Europe/Bratislava'))
            ny_time = date_index.astimezone(pytz.timezone('US/Eastern'))
            logger.info(f"{date_index.strftime('%Y-%m-%d %H:%M UTC')} | "
                       f"{local_time.strftime('%H:%M BA')} | "
                       f"{ny_time.strftime('%H:%M NY')} | "
                       f"O={row['open']:.2f} H={row['high']:.2f} L={row['low']:.2f} C={row['close']:.2f}")

        # Simulujeme výber signálnej sviečky presne ako v hlavnom skripte
        logger.info(f"\n=== VÝBER SIGNÁLNEJ SVIEČKY (SIMULÁCIA HLAVNÉHO SKRIPTU) ===")

        df_index_utc = df.index
        selected_bar_series = None

        if len(df_index_utc) > 0:
            timestamp_at_minus_1 = df_index_utc[-1]
            logger.info(f"Kandidát na signálnu sviečku (iloc[-1]) začína (UTC): {timestamp_at_minus_1.strftime('%Y-%m-%d %H:%M:%S %Z')}")

            if abs((timestamp_at_minus_1 - expected_signal_bar_start_utc).total_seconds()) < 10:
                selected_bar_series = df.iloc[-1]
                logger.info(f"Signálna sviečka (po explicitnom endDateTime) zvolená: iloc[-1].")
            else:
                logger.warning(f"NESÚLAD ČASU! Očakávaný ({expected_signal_bar_start_utc.strftime('%H:%M:%S %Z')}) "
                              f"sa nezhoduje s iloc[-1] ({timestamp_at_minus_1.strftime('%Y-%m-%d %H:%M:%S %Z')}).")
                if len(df_index_utc) >= 2:
                    timestamp_at_minus_2 = df_index_utc[-2]
                    if abs((timestamp_at_minus_2 - expected_signal_bar_start_utc).total_seconds()) < 10:
                        selected_bar_series = df.iloc[-2]
                        logger.warning(f"Používam iloc[-2] ako fallback po nesúlade času pre iloc[-1]. Čas iloc[-2] (UTC): {timestamp_at_minus_2.strftime('%Y-%m-%d %H:%M:%S %Z')}")

        if selected_bar_series is not None:
            signal_time = selected_bar_series.name
            ny_time = signal_time.astimezone(pytz.timezone('US/Eastern'))
            logger.info(f"\n=== VYBRANÁ SIGNÁLNA SVIEČKA ===")
            logger.info(f"Čas začiatku (UTC): {signal_time.strftime('%Y-%m-%d %H:%M:%S %Z')}")
            logger.info(f"Čas začiatku (NY): {ny_time.strftime('%Y-%m-%d %H:%M:%S %Z')}")
            logger.info(f"OPEN: {selected_bar_series['open']:.2f}")
            logger.info(f"HIGH: {selected_bar_series['high']:.2f}")
            logger.info(f"LOW: {selected_bar_series['low']:.2f}")
            logger.info(f"CLOSE: {selected_bar_series['close']:.2f}")

            # Porovnáme s očakávanými hodnotami
            if ny_time.hour == 19:  # 19:00 NY time sviečka (23:00 UTC)
                logger.info(f"\n=== ANALÝZA: Toto je 19:00-20:00 NY time sviečka ===")
                logger.info(f"Bot vybral sviečku 19:00-20:00 NY time, ale vy očakávate 20:00-21:00 NY time")
                logger.info(f"Problém je v tom, že endDateTime={end_time} končí o 01:59:59 UTC")
                logger.info(f"Takže posledná sviečka je 23:00-00:00 UTC (19:00-20:00 NY time)")
                logger.info(f"Ale vy chcete sviečku 00:00-01:00 UTC (20:00-21:00 NY time)")
            elif ny_time.hour == 20:  # 20:00 NY time sviečka (00:00 UTC)
                logger.info(f"\n=== ANALÝZA: Toto je 20:00-21:00 NY time sviečka ===")
                logger.info(f"Bot správne vybral sviečku 20:00-21:00 NY time")

                # Porovnáme s vašimi očakávanými hodnotami
                expected_open = 3282.6
                expected_close = 3288.6

                logger.info(f"\n=== POROVNANIE S OČAKÁVANÝMI HODNOTAMI ===")
                logger.info(f"Očakávaný OPEN: {expected_open:.2f} | Bot OPEN: {selected_bar_series['open']:.2f} | Rozdiel: {selected_bar_series['open'] - expected_open:.2f}")
                logger.info(f"Očakávaný CLOSE: {expected_close:.2f} | Bot CLOSE: {selected_bar_series['close']:.2f} | Rozdiel: {selected_bar_series['close'] - expected_close:.2f}")
        else:
            logger.error("Nepodarilo sa vybrať signálnu sviečku!")

        # Dodatočná analýza - pozrieme sa na všetky dostupné sviečky
        logger.info(f"\n=== VŠETKY DOSTUPNÉ SVIEČKY ===")
        for date_index, row in df.iterrows():
            ny_time = date_index.astimezone(pytz.timezone('US/Eastern'))
            logger.info(f"{date_index.strftime('%Y-%m-%d %H:%M UTC')} | "
                       f"{ny_time.strftime('%Y-%m-%d %H:%M NY')} | "
                       f"O={row['open']:.2f} H={row['high']:.2f} L={row['low']:.2f} C={row['close']:.2f}")

        # Záver
        logger.info(f"\n=== ZÁVER ===")
        logger.info(f"Hlavný skript používa endDateTime = theoretical_new_bar_start_utc - 1 sekunda")
        logger.info(f"Ak bot beží o 2:00:11 UTC, tak endDateTime = 1:59:59 UTC")
        logger.info(f"To znamená, že posledná sviečka v dátach je 23:00-00:00 UTC (19:00-20:00 NY time)")
        logger.info(f"Ale vy očakávate sviečku 00:00-01:00 UTC (20:00-21:00 NY time)")
        logger.info(f"")
        logger.info(f"RIEŠENIE: Bot by mal bežať o 3:00 UTC namiesto 2:00 UTC")
        logger.info(f"Alebo by sa mal zmeniť výpočet endDateTime")

    except Exception as e:
        logger.error(f"Chyba: {e}", exc_info=True)
    finally:
        if ib.isConnected():
            ib.disconnect()

if __name__ == "__main__":
    debug_mgc_data()
