# config.py

# ---------- KONFIGURÁCIA PRE BOTA ----------

# <PERSON><PERSON><PERSON><PERSON><PERSON> nastavenia
#  1 secs, 5 secs, 10 secs, 15 secs, 30 secs, 1 min, 2 mins, 3 mins, 5 mins, 10 mins, 15 mins, 20 mins, 30 mins, 1 hour, 2 hours, 3 hours, 4 hours, 8 hours, 1 day, 1W, 1M
TIMEFRAME = '1 hour'
QUANTITY = 1

# Hodnoty pre futures kontrakty (v tickoch)
SL_PTS_LONG     = 70
TRAIL_PTS_LONG  = 40
TRAIL_OFFSET_LONG = 1
SL_PTS_SHORT    = 40
TRAIL_PTS_SHORT = 20
TRAIL_OFFSET_SHORT = 1

# Konfigurácia pre uzatváranie pozícií pred koncom obchodného dňa
CLOSE_MINUTES_BEFORE_END = 15 # Zvýšené pre lepšie testovanie EOD
MARKET_CLOSE_HOUR = 17 # ET (napr. 17 pre 17:00 ET)
CHECK_EOD_CLOSURE_ENABLED = True # Na zapnutie/vypnutie EOD logiky

# Futures kontrakty (ponechan<PERSON> len tie, ktoré podľa vás fungovali lepšie)
INSTRUMENTS_CONFIG = [
    {'symbol': 'M2K', 'exchange': 'CME', 'multiplier': 5, 'secType': 'FUTURE', 'currency': 'USD'},
    {'symbol': 'MES', 'exchange': 'CME', 'multiplier': 5, 'secType': 'FUTURE', 'currency': 'USD'},
    {'symbol': 'MNQ', 'exchange': 'CME', 'multiplier': 2, 'secType': 'FUTURE', 'currency': 'USD'},
    {'symbol': 'MGC', 'exchange': 'COMEX', 'multiplier': 10, 'secType': 'FUTURE', 'currency': 'USD'},
    # Dočasne odstránené pre testovanie stability:
    # {'symbol': 'M6A', 'exchange': 'CME', 'multiplier': 10000, 'secType': 'FUTURE', 'currency': 'USD'},
    # {'symbol': 'M6B', 'exchange': 'CME', 'multiplier': 6250, 'secType': 'FUTURE', 'currency': 'USD'},
    # {'symbol': 'M6E', 'exchange': 'CME', 'multiplier': 12500, 'secType': 'FUTURE', 'currency': 'USD'},
]

# Tick sizes pre jednotlivé inštrumenty
TICK_SIZES = {
    'M6A': 0.00005, 'M6B': 0.0001, 'M6E': 0.00005,
    'MES': 0.25, 'MNQ': 0.25, 'M2K': 0.1, 'MGC': 0.1
}

# Telegram - DÔLEŽITÉ: Nahraďte vašimi skutočnými hodnotami!
TELEGRAM_TOKEN = '**********************************************'
TELEGRAM_CHAT_ID = '91797520'

# Názov CSV súboru pre logovanie obchodov
TRADES_CSV_FILE = 'trades_log.csv' # Malo by sa vytvoriť v adresári, kde beží skript

# Pripojenie k IB
IB_HOST = '127.0.0.1'
IB_PORT = 4001 
IB_CLIENT_ID = 1 

# Logging
LOG_LEVEL = 'DEBUG' # Pre testovanie nastavte na DEBUG
LOG_FORMAT = '%(asctime)s %(levelname)s:%(filename)s:%(lineno)d:%(name)s:%(message)s'
LOG_DATE_FORMAT = '%Y-%m-%d %H:%M:%S'

# Ostatné
MAX_CONNECT_ATTEMPTS = 5 # Počet pokusov o pripojenie pri štarte alebo bežnej chybe
MAX_GATEWAY_RESTART_CONNECT_ATTEMPTS = 15 # Viac pokusov pri ConnectionRefused (reštart Gateway)
DATA_REQUEST_ATTEMPTS = 3
ORDER_ID_WAIT_SECONDS = 10 
POSITION_CONFIRM_ATTEMPTS = 3
POSITION_CONFIRM_WAIT_SECONDS = 5 
MAX_CONSECUTIVE_CRASHES_LIMIT = 5 # Po koľkých pádoch čakať dlhšie