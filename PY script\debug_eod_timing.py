#!/usr/bin/env python3

import logging
import pytz
from datetime import datetime
import config

# Nastavenie logovania
logging.basicConfig(
    level=getattr(logging, config.LOG_LEVEL, logging.INFO),
    format=config.LOG_FORMAT,
    datefmt=config.LOG_DATE_FORMAT
)
logger = logging.getLogger(__name__)

def debug_eod_timing():
    """Debuguje EOD timing pre rôzne časy"""
    
    logger.info("=== DEBUG EOD TIMING ===")
    
    # Test pre rôzne časy 29.5.2025
    test_times_utc = [
        datetime(2025, 5, 29, 6, 0, 0, tzinfo=pytz.UTC),   # 6:00 UTC
        datetime(2025, 5, 29, 20, 0, 0, tzinfo=pytz.UTC),  # 20:00 UTC  
        datetime(2025, 5, 29, 20, 45, 0, tzinfo=pytz.UTC), # 20:45 UTC
        datetime(2025, 5, 29, 20, 55, 0, tzinfo=pytz.UTC), # 20:55 UTC
        datetime(2025, 5, 29, 21, 0, 0, tzinfo=pytz.UTC),  # 21:00 UTC
    ]
    
    for test_time in test_times_utc:
        logger.info(f"\n--- Test pre {test_time.strftime('%Y-%m-%d %H:%M:%S UTC')} ---")
        
        # Simulujeme is_near_market_close() logiku
        try:
            eastern = pytz.timezone('US/Eastern')
        except pytz.exceptions.UnknownTimeZoneError:
            logger.error("Časové pásmo 'US/Eastern' nebolo nájdené")
            continue

        now_utc = test_time
        now_et = now_utc.astimezone(eastern)
        
        market_close_hour_et = getattr(config, 'MARKET_CLOSE_HOUR', 17)
        close_minutes_param = getattr(config, 'CLOSE_MINUTES_BEFORE_END', 5)
        
        logger.info(f"UTC: {now_utc.strftime('%H:%M:%S')}")
        logger.info(f"ET:  {now_et.strftime('%H:%M:%S %Z')}")
        
        if now_et.weekday() >= 5: 
            logger.info(f"Je víkend ({now_et.strftime('%A')}), EOD sa neuplatňuje")
            continue
        
        close_time_et = now_et.replace(hour=market_close_hour_et, minute=0, second=0, microsecond=0)

        if now_et >= close_time_et: 
            logger.info(f"Aktuálny čas ET je už po zatváracom čase ET {close_time_et.strftime('%H:%M:%S')}")
            continue
        
        time_diff_minutes = (close_time_et - now_et).total_seconds() / 60
        is_eod_window = 0 < time_diff_minutes <= close_minutes_param
        
        logger.info(f"Zatvorenie trhu (ET): {close_time_et.strftime('%H:%M:%S')}")
        logger.info(f"Zostáva: {time_diff_minutes:.1f} minút")
        logger.info(f"EOD okno ({close_minutes_param}min): {is_eod_window}")
        
        if is_eod_window:
            logger.info("🔴 POZÍCIE BY SA MALI UZAVRIEŤ!")
        else:
            logger.info("🟢 Pozície sa neuzatvárajú")
    
    # Dodatočná analýza
    logger.info(f"\n=== ANALÝZA KONFIGURÁCIE ===")
    logger.info(f"MARKET_CLOSE_HOUR: {config.MARKET_CLOSE_HOUR}")
    logger.info(f"CLOSE_MINUTES_BEFORE_END: {config.CLOSE_MINUTES_BEFORE_END}")
    logger.info(f"CHECK_EOD_CLOSURE_ENABLED: {config.CHECK_EOD_CLOSURE_ENABLED}")
    
    # Správny čas pre EOD
    logger.info(f"\n=== SPRÁVNY ČAS PRE EOD ===")
    logger.info(f"Trh sa zatvorí o 17:00 ET")
    logger.info(f"EOD by sa mal spustiť o {17 - config.CLOSE_MINUTES_BEFORE_END/60:.2f}:XX ET")
    
    # Konverzia na UTC
    et_tz = pytz.timezone('US/Eastern')
    eod_time_et = datetime(2025, 5, 29, 17 - config.CLOSE_MINUTES_BEFORE_END//60, 
                          60 - config.CLOSE_MINUTES_BEFORE_END%60, 0)
    eod_time_et = et_tz.localize(eod_time_et)
    eod_time_utc = eod_time_et.astimezone(pytz.UTC)
    
    logger.info(f"EOD čas ET: {eod_time_et.strftime('%H:%M:%S %Z')}")
    logger.info(f"EOD čas UTC: {eod_time_utc.strftime('%H:%M:%S %Z')}")

if __name__ == "__main__":
    debug_eod_timing()
