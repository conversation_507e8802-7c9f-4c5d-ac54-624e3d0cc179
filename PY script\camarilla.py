#!/usr/bin/env python3
# camarilla.py

import logging
import time
from datetime import datetime, timedelta
from ib_insync import IB, util, Order, MarketOrder, Contract
import pandas as pd
import pytz
import sys
import os

import config
import utils
import ib_interface

logger = logging.getLogger(__name__)

ib = IB()
last_closed_conids_session = set()
current_active_expiry = None
instruments_data_list = []
theoretical_new_bar_start_utc = None
needs_data_initialization = True


def initialize_instruments_data():
    global instruments_data_list, current_active_expiry, ib, needs_data_initialization
    logger.info("Inicializujem/Reinicializujem dáta inštrumentov...")

    previous_positions_state = {
        inst_data.get('symbol'): {
            'inPosition': inst_data.get('inPosition', False),
            'entry_signal': inst_data.get('entry_signal', '')
        } for inst_data in instruments_data_list if inst_data.get('symbol')
    }

    temp_instruments_data_list = [] # Použijeme dočasný zoznam
    if current_active_expiry is None:
        current_active_expiry = utils.get_front_contract_month()
        logger.info(f"Počiatočný mesiac expirácie nastavený na: {current_active_expiry}")

    if not config.INSTRUMENTS_CONFIG:
        logger.warning("Zoznam INSTRUMENTS_CONFIG v config.py je prázdny!")
        instruments_data_list = [] # Uistíme sa, že je prázdny
        needs_data_initialization = False
        return

    for inst_conf in config.INSTRUMENTS_CONFIG:
        inst_data_new = inst_conf.copy()
        inst_data_new.update({
            'contract': None, 'inPosition': False, 'entry_price': 0.0,
            'entry_signal': '', 'entry_time': '', 'entry_timestamp': 0.0,
            'sl_order_id': None, 'sl_price': 0.0, 'trailing_set': False,
            'trail_offset_reached': False, 'trail_activation_price': 0.0,
            'trail_offset': 0.0, 'last_bar_processed_utc': None
        })

        symbol = inst_data_new['symbol']
        try:
            logger.info(f"[{symbol}] Načítavam kontrakt pre expiráciu {current_active_expiry}...")
            inst_data_new['contract'] = ib_interface.fetch_ib_contract(
                ib, symbol, inst_data_new['exchange'],
                current_active_expiry, inst_data_new['secType'], inst_data_new['currency']
            )
            if inst_data_new['contract']:
                logger.info(f"[{symbol}] Kontrakt úspešne načítaný: {getattr(inst_data_new['contract'], 'localSymbol', symbol)}")
                if symbol in previous_positions_state and previous_positions_state[symbol]['inPosition']:
                    logger.warning(f"[{symbol}] Po (re)inicializácii kontraktov bol tento inštrument predtým v pozícii ({previous_positions_state[symbol]['entry_signal']}). "
                                   f"Interný stav 'inPosition' je teraz False. Nutná synchronizácia.")
            else:
                logger.error(f"[{symbol}] Nepodarilo sa načítať kontrakt pri (re)inicializácii pre exp. {current_active_expiry}.")
        except Exception as e_init_contract_loop:
            logger.error(f"[{symbol}] Chyba pri načítaní kontraktu pri (re)inicializácii: {e_init_contract_loop}", exc_info=True)

        temp_instruments_data_list.append(inst_data_new)

    instruments_data_list = temp_instruments_data_list # Priradíme až po úspešnom naplnení dočasného zoznamu
    valid_contracts_count = sum(1 for i in instruments_data_list if i.get('contract') is not None)
    logger.info(f"Inicializovaných/Reinicializovaných {len(instruments_data_list)} inštrumentov. Platných kontraktov: {valid_contracts_count}")
    needs_data_initialization = False


def safe_process_dataframe_index(df_input, symbol_for_log, data_type_log):
    if df_input is None or df_input.empty:
        logger.warning(f"[{symbol_for_log}] Prijatý prázdny alebo None DataFrame pre {data_type_log} dáta.")
        return None

    df = df_input.copy()

    if 'date' not in df.columns and not isinstance(df.index, pd.DatetimeIndex):
        logger.error(f"[{symbol_for_log}] Chýba stĺpec 'date' a index nie je DatetimeIndex v {data_type_log} dátach. Stĺpce: {list(df.columns)}")
        return None
    try:
        if 'date' in df.columns and not isinstance(df.index, pd.DatetimeIndex):
            # Overíme, či 'date' stĺpec nie je už náhodou DatetimeIndex (aj keď to nie je štandardné pre stĺpec)
            if pd.api.types.is_datetime64_any_dtype(df['date'].dtype):
                 df = df.set_index('date', drop=True)
            else: # Ak to nie je datetime, pokúsime sa konvertovať
                logger.debug(f"[{symbol_for_log}] Konvertujem stĺpec 'date' na datetime pre {data_type_log} dáta.")
                df['date'] = pd.to_datetime(df['date'], errors='coerce')
                df.dropna(subset=['date'], inplace=True)
                if df.empty:
                    logger.warning(f"[{symbol_for_log}] DataFrame je prázdny po odstránení neplatných dátumov v {data_type_log} dátach.")
                    return None
                df = df.set_index('date', drop=True)

        if not isinstance(df.index, pd.DatetimeIndex):
            logger.warning(f"[{symbol_for_log}] Index {data_type_log} dát nie je DatetimeIndex. Typ: {type(df.index)}. Skúšam pd.to_datetime(df.index).")
            try:
                original_index_name = df.index.name # Uložíme si názov indexu, ak existuje
                df.index = pd.to_datetime(df.index, errors='coerce')
                if original_index_name: df.index.name = original_index_name # Obnovíme názov

                if pd.NaT in df.index:
                    logger.warning(f"[{symbol_for_log}] Index obsahuje NaT hodnoty po konverzii, odstraňujem ich.")
                    df = df[df.index.notna()]
                if df.empty or not isinstance(df.index, pd.DatetimeIndex):
                     logger.error(f"[{symbol_for_log}] Index stále nie je DatetimeIndex ani po druhom pokuse alebo je prázdny.")
                     return None
            except Exception as e_conv_idx_direct:
                 logger.error(f"[{symbol_for_log}] Chyba pri druhom pokuse o konverziu indexu na DatetimeIndex: {e_conv_idx_direct}")
                 return None

        if df.index.tz is None: # Ak je naivný
            logger.debug(f"[{symbol_for_log}] {data_type_log} index je naivný, lokalizujem na UTC.")
            try:
                df.index = df.index.tz_localize(pytz.utc, ambiguous='infer', nonexistent='shift_forward')
            except Exception as e_tz_loc:
                logger.error(f"[{symbol_for_log}] Chyba pri tz_localize {data_type_log} na UTC: {e_tz_loc}. Dáta budú preskočené.")
                return None
        else: # Ak je už uvedomelý
            logger.debug(f"[{symbol_for_log}] {data_type_log} index je uvedomelý ({df.index.tz}), konvertujem na UTC.")
            df.index = df.index.tz_convert(pytz.utc)

        if df.empty:
            logger.warning(f"[{symbol_for_log}] DataFrame pre {data_type_log} je prázdny po spracovaní indexu.")
            return None

        return df
    except Exception as e_process_idx:
        logger.error(f"[{symbol_for_log}] Všeobecná chyba pri spracovaní indexu {data_type_log} dát: {e_process_idx}", exc_info=True)
        return None


def run_main_trading_loop():
    global last_closed_conids_session, current_active_expiry, instruments_data_list, ib
    global theoretical_new_bar_start_utc, needs_data_initialization

    if needs_data_initialization:
        logger.info("run_main_trading_loop: Potrebná (re)inicializácia dát inštrumentov.")
        initialize_instruments_data()

    active_instruments_to_process = [inst for inst in instruments_data_list if inst.get('contract')]
    if not active_instruments_to_process:
        logger.error("run_main_trading_loop: Žiadne platné inštrumenty s kontraktami na spracovanie. Čakám 60s.")
        needs_data_initialization = True
        time.sleep(60)
        return

    if theoretical_new_bar_start_utc is None:
        logger.info("run_main_trading_loop: Prvá barová iterácia, čakám na prvú celú sviečku...")

    theoretical_new_bar_start_utc = utils.wait_for_next_bar(config.TIMEFRAME)
    logger.info(f"Nová perióda začína. Teoretický začiatok novej sviečky (UTC): {theoretical_new_bar_start_utc.strftime('%Y-%m-%d %H:%M:%S %Z')}")

    tf_parts = config.TIMEFRAME.split()
    tf_value = int(tf_parts[0]) if len(tf_parts) > 0 and tf_parts[0].isdigit() else 1
    tf_unit = tf_parts[1].lower() if len(tf_parts) > 1 else "hour"
    if 'hour' in tf_unit: timeframe_delta = timedelta(hours=tf_value)
    elif 'min' in tf_unit: timeframe_delta = timedelta(minutes=tf_value)
    else: timeframe_delta = timedelta(hours=1); logger.error(f"Neznámy TIMEFRAME unit: {config.TIMEFRAME}")

    expected_signal_bar_start_utc = theoretical_new_bar_start_utc - timeframe_delta
    logger.info(f"Pre tento cyklus je očakávaný začiatok signálnej sviečky (UTC): {expected_signal_bar_start_utc.strftime('%Y-%m-%d %H:%M:%S %Z')}")

    newly_fetched_expiry = utils.get_front_contract_month()
    if newly_fetched_expiry != current_active_expiry:
        logger.info(f"Detekovaný rollover z {current_active_expiry} na {newly_fetched_expiry}.")
        current_active_expiry = newly_fetched_expiry
        needs_data_initialization = True
        logger.info("Po rolloveri bude nasledovať reinicializácia kontraktov v ďalšej iterácii __main__.")
        return # Ukončíme túto iteráciu, __main__ slučka zavolá znova a vykoná reinicializáciu

    if config.CHECK_EOD_CLOSURE_ENABLED and utils.is_near_market_close():
        logger.info("EOD CHECK: Podmienka is_near_market_close() je TRUE. Kontrolujem pozície na uzavretie.")
        any_position_closed_eod_flag = False
        for inst_data_eod_item in active_instruments_to_process:
            symbol_eod_item = inst_data_eod_item['symbol']
            contract_eod_item = inst_data_eod_item.get('contract')

            # Najprv overíme skutočný stav pozície v IB
            current_ib_positions_eod = ib.positions()
            actual_position_size_eod = 0
            for p_eod in current_ib_positions_eod:
                if p_eod.contract.conId == contract_eod_item.conId:
                    actual_position_size_eod = p_eod.position
                    break

            if actual_position_size_eod != 0 and contract_eod_item:
                # Určíme správny smer uzatvárania na základe skutočnej pozície
                if actual_position_size_eod > 0:
                    action_eod_item = 'SELL'
                    position_type_eod = 'LONG'
                else:
                    action_eod_item = 'BUY'
                    position_type_eod = 'SHORT'

                # Použijeme skutočnú veľkosť pozície
                quantity_to_close = abs(actual_position_size_eod)

                logger.warning(f"[{symbol_eod_item}] EOD: Pozícia {position_type_eod} s veľkosťou {actual_position_size_eod} je otvorená. Pokúšam sa ju uzavrieť.")
                try:
                    eod_close_order_obj = MarketOrder(action_eod_item, quantity_to_close)
                    eod_close_order_obj.outsideRth = True
                    trade_eod_submission = ib.placeOrder(contract_eod_item, eod_close_order_obj)
                    status_eod_msg = trade_eod_submission.orderStatus.status if trade_eod_submission and trade_eod_submission.orderStatus else 'N/A'
                    logger.info(f"[{symbol_eod_item}] EOD: Príkaz na uzavretie zadaný. Stav: {status_eod_msg}")

                    utils.send_telegram(f"[{symbol_eod_item}] EOD: Príkaz na uzavretie pozície {position_type_eod} s veľkosťou {quantity_to_close} zadaný.")

                    ib.sleep(5)
                    exit_price_eod_val = inst_data_eod_item.get('entry_price', 0.0); pnl_eod_val = 0.0
                    all_fills_eod = ib.fills()
                    for fill_eod in reversed(all_fills_eod):
                        if fill_eod.contract.conId == contract_eod_item.conId:
                            fill_time_eod_str = fill_eod.execution.time; fill_time_eod_dt = None
                            try:
                                # Ak je už datetime objekt, použijeme ho priamo
                                if isinstance(fill_time_eod_str, datetime):
                                    fill_time_eod_dt = fill_time_eod_str.astimezone(pytz.utc) if fill_time_eod_str.tzinfo else pytz.utc.localize(fill_time_eod_str)
                                else:
                                    fill_time_eod_dt = datetime.strptime(fill_time_eod_str, '%Y%m%d  %H:%M:%S')
                                    gateway_tz_str = "America/New_York"
                                    fill_time_eod_dt = pytz.timezone(gateway_tz_str).localize(fill_time_eod_dt, is_dst=None).astimezone(pytz.utc)
                            except Exception as e_fill_time_eod:
                                logger.warning(f"[{symbol_eod_item}] EOD: Nepodarilo sa parsovať/konvertovať čas fillu: {fill_time_eod_str}, chyba: {e_fill_time_eod}")

                            # Kontrola či je fill z posledných 30 minút (EOD uzatváranie)
                            now_utc = datetime.now(pytz.utc)
                            if fill_time_eod_dt and (now_utc - fill_time_eod_dt).total_seconds() < 1800:  # 30 minút
                                if (fill_eod.execution.side == 'SLD' and position_type_eod == 'LONG') or \
                                   (fill_eod.execution.side == 'BOT' and position_type_eod == 'SHORT'):
                                    exit_price_eod_val = fill_eod.execution.price
                                    entry_p_eod = inst_data_eod_item.get('entry_price', exit_price_eod_val)
                                    if isinstance(entry_p_eod, (int,float)) and isinstance(exit_price_eod_val, (int,float)):
                                        pnl_pts_eod = (exit_price_eod_val - entry_p_eod) if position_type_eod == 'LONG' else (entry_p_eod - exit_price_eod_val)
                                        pnl_eod_val = pnl_pts_eod * inst_data_eod_item['multiplier'] * quantity_to_close
                                        logger.info(f"[{symbol_eod_item}] EOD uzavretie, cena z fills: {exit_price_eod_val}, PNL: {pnl_eod_val:.2f}")
                                    break

                    if utils.append_trade_to_csv(
                         symbol_eod_item, position_type_eod, inst_data_eod_item.get('entry_price',0.0),
                         exit_price_eod_val, pnl_eod_val,
                         inst_data_eod_item.get('entry_time','EOD Exit'), inst_data_eod_item.get('sl_price',0.0),
                         inst_data_eod_item.get('trailing_set', False)
                    ): logger.info(f"[{symbol_eod_item}] EOD obchod zapísaný do CSV.")
                    else: logger.error(f"[{symbol_eod_item}] EOD obchod sa NEPODARILO zapísať do CSV.")

                    # Resetujeme interný stav
                    inst_data_eod_item.update({'inPosition': False, 'trailing_set': False, 'sl_order_id': None, 'entry_signal': '', 'entry_price': 0.0, 'entry_time': '', 'entry_timestamp': 0.0})
                    if contract_eod_item.conId not in last_closed_conids_session:
                         last_closed_conids_session.add(contract_eod_item.conId)
                    any_position_closed_eod_flag = True
                except Exception as e_eod_inst_close_loop:
                    logger.error(f"[{symbol_eod_item}] Chyba pri EOD uzatváraní pozície: {e_eod_inst_close_loop}", exc_info=True)
            elif contract_eod_item:
                logger.debug(f"[{symbol_eod_item}] EOD: Žiadna otvorená pozícia na uzavretie (IB pozícia: {actual_position_size_eod}).")

        if not any_position_closed_eod_flag and any(inst.get('inPosition') for inst in active_instruments_to_process):
            logger.info("EOD: Neboli nájdené žiadne pozície na uzavretie, alebo už boli spracované skôr.")
        elif not any(inst.get('inPosition') for inst in active_instruments_to_process):
             logger.info("EOD: Žiadne otvorené pozície na uzavretie.")


    for inst_data in active_instruments_to_process:
        symbol = inst_data['symbol']
        contract_obj = inst_data['contract']

        if inst_data.get('last_bar_processed_utc') == theoretical_new_bar_start_utc:
            logger.debug(f"[{symbol}] Signál pre periódu končiacu {theoretical_new_bar_start_utc.strftime('%Y-%m-%d %H:%M %Z')} už bol spracovaný. Preskakujem.")
            continue

        logger.info(f"--- Začínam spracovanie pre {symbol} pre sviečku končiacu pred {theoretical_new_bar_start_utc.strftime('%Y-%m-%d %H:%M:%S %Z')} ---")

        try:
            # 1. Denné dáta pre pivoty
            daily_bars_df = None; h4_d = None; l4_d = None
            for attempt_daily in range(config.DATA_REQUEST_ATTEMPTS):
                daily_bars_raw = None; temp_df_daily = None
                try:
                    logger.debug(f"[{symbol}] Pokus {attempt_daily+1} o stiahnutie denných dát.")
                    daily_bars_raw = ib.reqHistoricalData(contract_obj, '', '5 D', '1 day', 'ASK', False, formatDate=1)
                except Exception as e_fetch_daily_loop_inner:
                    logger.warning(f"[{symbol}] Výnimka pri reqHistoricalData pre denné dáta (pokus {attempt_daily+1}): {e_fetch_daily_loop_inner}")

                if daily_bars_raw:
                    temp_df_daily = util.df(daily_bars_raw)
                    daily_bars_df = safe_process_dataframe_index(temp_df_daily, symbol, "denných")
                    if daily_bars_df is not None and not daily_bars_df.empty: break
                logger.warning(f"[{symbol}] Denné dáta neboli prijaté alebo spracované (pokus {attempt_daily+1}).")
                if attempt_daily < config.DATA_REQUEST_ATTEMPTS - 1: ib.sleep(2)

            if daily_bars_df is not None and not daily_bars_df.empty and len(daily_bars_df) >= 2:
                h4_d, l4_d = utils.calc_pivots(daily_bars_df, symbol)

            if h4_d is None or l4_d is None:
                logger.warning(f"[{symbol}] Pivoty neboli vypočítané alebo chýbajú dáta. Preskakujem signály.")
                inst_data['last_bar_processed_utc'] = theoretical_new_bar_start_utc
                continue

            # 2. Dáta pre časový rámec stratégie
            tf_bars_df = None
            end_datetime_for_tf_request = theoretical_new_bar_start_utc - timedelta(seconds=1)
            duration_for_tf_request = '1 D'

            if end_datetime_for_tf_request and isinstance(end_datetime_for_tf_request, datetime):
                logger.debug(f"[{symbol}] Požadujem TF dáta s endDateTime (UTC): {end_datetime_for_tf_request.strftime('%Y%m%d %H:%M:%S %Z')} a duration {duration_for_tf_request}")
                for attempt_tf in range(config.DATA_REQUEST_ATTEMPTS):
                    tf_bars_raw = None; temp_df_tf = None
                    try:
                        logger.debug(f"[{symbol}] Pokus {attempt_tf+1} o stiahnutie TF dát s upraveným endDateTime.")
                        tf_bars_raw = ib.reqHistoricalData(
                            contract_obj, endDateTime=end_datetime_for_tf_request,
                            durationStr=duration_for_tf_request, barSizeSetting=config.TIMEFRAME,
                            whatToShow='ASK', useRTH=False, formatDate=1
                        )
                    except Exception as e_fetch_tf_loop_inner:
                         logging.warning(f"[{symbol}] Výnimka pri reqHistoricalData pre TF dáta (pokus {attempt_tf+1}): {e_fetch_tf_loop_inner}")

                    if tf_bars_raw:
                        temp_df_tf = util.df(tf_bars_raw)
                        tf_bars_df = safe_process_dataframe_index(temp_df_tf, symbol, f"{config.TIMEFRAME} (s endDateTime)")
                        if tf_bars_df is not None and not tf_bars_df.empty:
                            logger.debug(f"[{symbol}] Počet prijatých TF sviečok: {len(tf_bars_df)}")
                            break
                    logging.warning(f"[{symbol}] Pre TF dáta neboli prijaté alebo spracované žiadne sviečky (pokus {attempt_tf+1}).")
                    if attempt_tf < config.DATA_REQUEST_ATTEMPTS - 1: ib.sleep(2)
            else:
                 logger.error(f"[{symbol}] end_datetime_for_tf_request nie je platný datetime objekt: {end_datetime_for_tf_request}")

            if tf_bars_df is None or tf_bars_df.empty:
                logger.error(f"[{symbol}] {config.TIMEFRAME} dáta nie sú k dispozícii po pokusoch. Preskakujem inštrument.")
                inst_data['last_bar_processed_utc'] = theoretical_new_bar_start_utc
                continue

            tf_bars_df['ema8'] = tf_bars_df['close'].ewm(span=8, adjust=False).mean()

            selected_bar_series = None
            df_index_utc = tf_bars_df.index

            if not isinstance(expected_signal_bar_start_utc, datetime):
                logger.error(f"[{symbol}] expected_signal_bar_start_utc nie je datetime ({type(expected_signal_bar_start_utc)}). Preskakujem dynamický výber.")
                inst_data['last_bar_processed_utc'] = theoretical_new_bar_start_utc
                continue

            if len(df_index_utc) > 0 :
                logger.debug(f"--- [{symbol}] DEBUG: TF_BARS_DF TAIL ---")
                logger.debug(f"[{symbol}] Očakávaný začiatok signálnej sviečky (UTC): {expected_signal_bar_start_utc.strftime('%Y-%m-%d %H:%M:%S %Z')}")
                try: logger.debug(f"[{symbol}] tf_bars_df.tail(3):\n{tf_bars_df.tail(3).to_string()}")
                except Exception: pass
                logger.debug(f"--- [{symbol}] KONIEC DEBUG ---")

                timestamp_at_minus_1 = df_index_utc[-1]
                if isinstance(timestamp_at_minus_1, pd.Timestamp):
                    logger.info(f"[{symbol}] Kandidát na signálnu sviečku (iloc[-1]) začína (UTC): {timestamp_at_minus_1.strftime('%Y-%m-%d %H:%M:%S %Z')}")
                    if abs((timestamp_at_minus_1 - expected_signal_bar_start_utc).total_seconds()) < 10:
                        selected_bar_series = tf_bars_df.iloc[-1]
                        logger.info(f"[{symbol}] Signálna sviečka (po explicitnom endDateTime) zvolená: iloc[-1].")
                    else:
                        logger.warning(f"[{symbol}] NESÚLAD ČASU! Očakávaný ({expected_signal_bar_start_utc.strftime('%H:%M:%S %Z')}) "
                                      f"sa nezhoduje s iloc[-1] ({timestamp_at_minus_1.strftime('%Y-%m-%d %H:%M:%S %Z')}).")
                        if len(df_index_utc) >= 2:
                            timestamp_at_minus_2 = df_index_utc[-2]
                            if isinstance(timestamp_at_minus_2, pd.Timestamp) and \
                               abs((timestamp_at_minus_2 - expected_signal_bar_start_utc).total_seconds()) < 10:
                                selected_bar_series = tf_bars_df.iloc[-2]
                                logger.warning(f"[{symbol}] Používam iloc[-2] ako fallback po nesúlade času pre iloc[-1]. Čas iloc[-2] (UTC): {timestamp_at_minus_2.strftime('%Y-%m-%d %H:%M:%S %Z')}")
                else:
                     logger.warning(f"[{symbol}] Index na iloc[-1] nie je Timestamp ({type(timestamp_at_minus_1)}), nemôžem porovnať čas.")

            if selected_bar_series is None:
                logger.error(f"[{symbol}] Nepodarilo sa určiť signálnu sviečku. Posledná dostupná sviečka v DF (ak existuje): {df_index_utc[-1].strftime('%Y-%m-%d %H:%M:%S %Z') if len(df_index_utc) > 0 else 'žiadna'}. Preskakujem.")
                inst_data['last_bar_processed_utc'] = theoretical_new_bar_start_utc
                continue

            last_completed_bar = selected_bar_series
            try:
                lc = float(last_completed_bar['close'])
                lo = float(last_completed_bar['open'])
                if 'ema8' not in last_completed_bar.index or pd.isna(last_completed_bar['ema8']):
                    le = lc; logger.warning(f"[{symbol}] Chýba platná EMA8 pre {last_completed_bar.name.strftime('%Y-%m-%d %H:%M:%S %Z')}, použijem lc.")
                else:
                    le = float(last_completed_bar['ema8'])
            except (TypeError, KeyError, ValueError) as e_ohlc_final_extract:
                logger.error(f"[{symbol}] Chyba pri získavaní OHLC/EMA z vybranej sviečky: {e_ohlc_final_extract}. Sviečka: {last_completed_bar.to_dict() if isinstance(last_completed_bar, pd.Series) else last_completed_bar}")
                inst_data['last_bar_processed_utc'] = theoretical_new_bar_start_utc
                continue

            price_format_log_signal = ".4f" if symbol in ['M6A', 'M6B', 'M6E'] else ".2f"
            log_ts_str_signal_bar = last_completed_bar.name.strftime('%Y-%m-%d %H:%M UTC')
            try:
                server_local_tz_log_signal = datetime.now().astimezone().tzinfo
                if server_local_tz_log_signal:
                    log_ts_str_signal_bar = last_completed_bar.name.astimezone(server_local_tz_log_signal).strftime('%Y-%m-%d %H:%M LOKAL')
            except Exception: pass
            logger.info(f"[{symbol}] Signálna sviečka ({log_ts_str_signal_bar}): O={lo:{price_format_log_signal}} C={lc:{price_format_log_signal}} EMA8={le:{price_format_log_signal}} || H4d={h4_d:{price_format_log_signal}} L4d={l4_d:{price_format_log_signal}}")

            # Detailná analýza signálnych podmienok
            long_cond1 = lc > h4_d  # close > H4
            long_cond2 = lo < h4_d  # open < H4
            long_cond3 = lc > le    # close > EMA8
            long_signal_active = long_cond1 and long_cond2 and long_cond3

            short_cond1 = lc < l4_d  # close < L4
            short_cond2 = lo > l4_d  # open > L4
            short_cond3 = lc < le    # close < EMA8
            short_signal_active = short_cond1 and short_cond2 and short_cond3

            logger.info(f"[{symbol}] Podmienky signálu: LONG={long_signal_active}, SHORT={short_signal_active}")
            logger.debug(f"[{symbol}] LONG detaily: C>H4({lc:.2f}>{h4_d:.2f})={long_cond1}, O<H4({lo:.2f}<{h4_d:.2f})={long_cond2}, C>EMA8({lc:.2f}>{le:.2f})={long_cond3}")
            logger.debug(f"[{symbol}] SHORT detaily: C<L4({lc:.2f}<{l4_d:.2f})={short_cond1}, O>L4({lo:.2f}>{l4_d:.2f})={short_cond2}, C<EMA8({lc:.2f}<{le:.2f})={short_cond3}")
            inst_data['last_bar_processed_utc'] = theoretical_new_bar_start_utc

            # Synchronizácia stavu pozície
            try:
                current_ib_positions = ib.positions()
                actual_position_size = 0
                for p in current_ib_positions:
                    if p.contract.conId == contract_obj.conId:
                        actual_position_size = p.position
                        break

                is_actually_in_position_ib = actual_position_size != 0
                current_internal_state = inst_data.get('inPosition', False)

                if current_internal_state != is_actually_in_position_ib:
                    logger.warning(f"[{symbol}] Nekonzistencia stavu pozície. Interný: {current_internal_state}, IB pozícia: {actual_position_size}. Aktualizujem.")
                    inst_data['inPosition'] = is_actually_in_position_ib
                    if not is_actually_in_position_ib:
                        inst_data.update({'trailing_set': False, 'sl_order_id': None, 'entry_signal': '', 'entry_price': 0.0, 'entry_time': '', 'entry_timestamp': 0.0})
                    else:
                        # Ak máme pozíciu v IB ale bot si myslí že nie, pokúsime sa určiť smer
                        if actual_position_size > 0:
                            inst_data['entry_signal'] = 'LONG'
                        elif actual_position_size < 0:
                            inst_data['entry_signal'] = 'SHORT'
                        logger.info(f"[{symbol}] Obnovený stav pozície: {inst_data['entry_signal']} s veľkosťou {actual_position_size}")

            except Exception as e_pos_sync_loop_main_final:
                logger.error(f"[{symbol}] Chyba pri synchronizácii stavu pozície: {e_pos_sync_loop_main_final}. Preskakujem vstup.")
                continue

            # Otváranie pozícií
            signal_to_act_on = None
            if long_signal_active: signal_to_act_on = 'LONG'
            elif short_signal_active: signal_to_act_on = 'SHORT'

            if signal_to_act_on and not inst_data.get('inPosition', False):
                logger.info(f"[{symbol}] {signal_to_act_on} signál aktívny. Overujem možnosť vstupu.")
                final_check_positions_before_entry = ib.positions()
                current_position_size = 0
                for p in final_check_positions_before_entry:
                    if p.contract.conId == contract_obj.conId:
                        current_position_size = p.position
                        break

                if current_position_size == 0:
                    logger.info(f"[{symbol}] Pokus o umiestnenie {signal_to_act_on} bracket príkazu s ref. cenou {lc:{price_format_log_signal}}")
                    if ib_interface.place_bracket_order(ib, inst_data, signal_to_act_on, lc):
                        position_confirmed_by_bot = False
                        for attempt_confirm_idx in range(config.POSITION_CONFIRM_ATTEMPTS):
                            time.sleep(config.POSITION_CONFIRM_WAIT_SECONDS)
                            positions_after_entry_check = ib.positions()
                            expected_pos_size = config.QUANTITY if signal_to_act_on == 'LONG' else -config.QUANTITY
                            current_pos_value = 0
                            pos_found_confirm = False
                            for p_confirm in positions_after_entry_check:
                                if p_confirm.contract.conId == contract_obj.conId:
                                    current_pos_value = p_confirm.position
                                    pos_found_confirm = True; break
                            if pos_found_confirm and current_pos_value == expected_pos_size:
                                logger.info(f"[{symbol}] Pozícia {signal_to_act_on} potvrdená v ib.positions() (veľkosť: {current_pos_value}, pokus {attempt_confirm_idx+1}).")
                                position_confirmed_by_bot = True; break
                            else:
                                logger.warning(f"[{symbol}] Pokus {attempt_confirm_idx+1}/{config.POSITION_CONFIRM_ATTEMPTS}: {signal_to_act_on} pozícia zatiaľ neviditeľná alebo nesprávna veľkosť (očakávaná: {expected_pos_size}, aktuálna: {current_pos_value}).")

                        if position_confirmed_by_bot:
                            inst_data.update({'inPosition': True, 'entry_price': lc, 'entry_signal': signal_to_act_on,
                                              'entry_time': datetime.now(pytz.utc).strftime('%Y-%m-%d %H:%M:%S %Z'),
                                              'entry_timestamp': time.time()})
                            # SL a Trail parametre sú nastavené v place_bracket_order a v inst_data
                            logger.info(f"[{symbol}] Sledovanie {signal_to_act_on} pozície inicializované.")
                        else:
                            logger.error(f"[{symbol}] {signal_to_act_on} pozícia NEBOLA potvrdená. Bot ju NEBUDE spravovať. Manuálna kontrola v TWS nutná!")
                            inst_data['inPosition'] = False
                    else:
                        logger.error(f"[{symbol}] ib_interface.place_bracket_order pre {signal_to_act_on} zlyhalo.")
                        inst_data['inPosition'] = False
                else:
                    logger.warning(f"[{symbol}] Preskakujem {signal_to_act_on} signál, pozícia už existuje s veľkosťou {current_position_size} (finálna kontrola).")
            elif signal_to_act_on and inst_data.get('inPosition', False):
                logger.info(f"[{symbol}] {signal_to_act_on} signál aktívny, ale bot už má pozíciu. Preskakujem.")

            # Manažment Trailing Stopu
            if inst_data.get('inPosition', False) and not inst_data.get('trailing_set', False) and \
               inst_data.get('entry_signal') and inst_data.get('trail_activation_price', 0) != 0:
                try:
                    logger.debug(f"[{symbol}] Kontrolujem podmienku pre trailing stop.")
                    market_price_for_trail = None
                    ticker_list_trail = ib.reqTickers(contract_obj)
                    ib.sleep(0.5)
                    if ticker_list_trail and ticker_list_trail[0]:
                        ticker_trail = ticker_list_trail[0]
                        if ticker_trail.last is not None and not pd.isna(ticker_trail.last): market_price_for_trail = ticker_trail.last
                        elif ticker_trail.close is not None and not pd.isna(ticker_trail.close): market_price_for_trail = ticker_trail.close
                        elif inst_data['entry_signal'] == 'LONG' and ticker_trail.ask is not None and not pd.isna(ticker_trail.ask): market_price_for_trail = ticker_trail.ask
                        elif inst_data['entry_signal'] == 'SHORT' and ticker_trail.bid is not None and not pd.isna(ticker_trail.bid): market_price_for_trail = ticker_trail.bid
                        if market_price_for_trail is not None: logger.debug(f"[{symbol}] Market cena pre trailing stop (reqTickers): {market_price_for_trail}")

                    if market_price_for_trail is None:
                        market_price_for_trail = lc
                        logger.warning(f"[{symbol}] Pre trailing stop sa nepodarilo získať aktuálnu market cenu, použijem close ({market_price_for_trail:{price_format_log_signal}}) poslednej TF sviečky.")

                    if market_price_for_trail is None:
                         logger.error(f"[{symbol}] Stále nemám platnú cenu pre kontrolu trailing stopu. Preskakujem.")
                         continue

                    logger.debug(f"[{symbol}] Pre trailing: Akt.cena={market_price_for_trail:{price_format_log_signal}}, Vstup={inst_data.get('entry_price',0):{price_format_log_signal}}, TrailAktiv.@ {inst_data.get('trail_activation_price',0):{price_format_log_signal}}")

                    trail_is_met = False; entry_s_trail = inst_data['entry_signal']; trail_ap_val = inst_data['trail_activation_price']
                    if isinstance(market_price_for_trail, (int, float)) and isinstance(trail_ap_val, (int, float)) and trail_ap_val != 0:
                        if entry_s_trail == 'LONG' and market_price_for_trail >= trail_ap_val: trail_is_met = True
                        elif entry_s_trail == 'SHORT' and market_price_for_trail <= trail_ap_val: trail_is_met = True

                    if trail_is_met:
                        logger.info(f"[{symbol}] Podmienka pre trailing stop splnená pri cene {market_price_for_trail:{price_format_log_signal}}.")
                        original_sl_order_id_trail = inst_data.get('sl_order_id')
                        if original_sl_order_id_trail:
                            try:
                                logger.info(f"[{symbol}] Ruším pôvodný SL príkaz ID: {original_sl_order_id_trail}.")
                                cancel_sl_order_obj_trail = Order(orderId=original_sl_order_id_trail)
                                ib.cancelOrder(cancel_sl_order_obj_trail)
                                logger.info(f"[{symbol}] Pôvodný SL príkaz {original_sl_order_id_trail} zrušený.")
                                inst_data['sl_order_id'] = None
                            except Exception as e_cancel_sl_trail_loop:
                                logger.error(f"[{symbol}] Chyba pri rušení pôvodného SL ({original_sl_order_id_trail}) pre TRAIL: {e_cancel_sl_trail_loop}.", exc_info=True)
                                utils.send_telegram(f"⚠️ [{symbol}] CHYBA pri rušení SL {original_sl_order_id_trail} pre TRAIL! Možný duplicitný SL!")
                        else: logger.warning(f"[{symbol}] Nebolo nájdené sl_order_id na zrušenie pred aktiváciou trail stopu.")

                        trail_action_cmd = 'SELL' if entry_s_trail == 'LONG' else 'BUY'
                        trail_order_obj_cmd = Order(orderType='TRAIL', action=trail_action_cmd, totalQuantity=config.QUANTITY, auxPrice=inst_data['trail_offset'])
                        trail_order_obj_cmd.outsideRth = True

                        placed_trail_trade_obj = ib.placeOrder(contract_obj, trail_order_obj_cmd)
                        inst_data['trailing_set'] = True

                        final_trail_order_id_val = None; ib.sleep(0.5)
                        if trail_order_obj_cmd.orderId and trail_order_obj_cmd.orderId != 0: final_trail_order_id_val = trail_order_obj_cmd.orderId

                        status_trail_msg_log = placed_trail_trade_obj.orderStatus.status if placed_trail_trade_obj and placed_trail_trade_obj.orderStatus else 'N/A'
                        msg_trail_final_log_send = f"[{symbol}] Trailing stop aktivovaný @ {market_price_for_trail:{price_format_log_signal}} s offsetom {inst_data['trail_offset']}. TRAIL ID: {final_trail_order_id_val if final_trail_order_id_val else 'N/A'} (Status: {status_trail_msg_log})"
                        logger.info(msg_trail_final_log_send)
                        utils.send_telegram(f"🔒 {msg_trail_final_log_send}")
                except Exception as e_manage_trailing_stop_loop:
                    logger.error(f"[{symbol}] Chyba pri manažmente trailing stopu: {e_manage_trailing_stop_loop}", exc_info=True)

            # Kontrola uzavretých pozícií
            if inst_data.get('inPosition', False):
                try:
                    current_positions_at_closure_check = ib.positions()
                    current_position_size_closure = 0
                    for p in current_positions_at_closure_check:
                        if p.contract.conId == contract_obj.conId:
                            current_position_size_closure = p.position
                            break

                    position_still_active_ib = current_position_size_closure != 0

                    if not position_still_active_ib:
                        logger.info(f"[{symbol}] Pozícia sa javí ako uzavretá. Vykonávam finálne overenie a spracovanie.")
                        time.sleep(config.POSITION_CONFIRM_WAIT_SECONDS)

                        final_recheck_positions = ib.positions()
                        final_position_size = 0
                        for p in final_recheck_positions:
                            if p.contract.conId == contract_obj.conId:
                                final_position_size = p.position
                                break

                        is_definitely_closed = final_position_size == 0

                        if is_definitely_closed:
                            logger.info(f"[{symbol}] Pozícia je potvrdená ako uzavretá po re-kontrole.")
                            closed_con_id_val = contract_obj.conId

                            if closed_con_id_val not in last_closed_conids_session:
                                # Okamžite pošleme základnú notifikáciu o uzavretí
                                price_format_for_closing_msg = ".4f" if symbol in ['M6A', 'M6B', 'M6E'] else ".2f"
                                basic_close_msg = f"🔴 [{symbol}] Pozícia {inst_data.get('entry_signal','N/A')} uzavretá"
                                logger.info(f"[{symbol}] Posielam základnú notifikáciu o uzavretí pozície")
                                utils.send_telegram(basic_close_msg)
                                exit_price_val_close = None; closure_type_str_close = "unknown"
                                price_format_for_closing_msg = ".4f" if symbol in ['M6A', 'M6B', 'M6E'] else ".2f"

                                all_current_fills_list = ib.fills()
                                logger.debug(f"[{symbol}] Počet prijatých záznamov o filloch pre uzavretie: {len(all_current_fills_list)}")
                                for current_fill_item_detail in reversed(all_current_fills_list):
                                    if current_fill_item_detail.contract.conId == closed_con_id_val:
                                        fill_exec_time_str_val_detail = current_fill_item_detail.execution.time
                                        fill_exec_time_dt_val_detail = None
                                        try:
                                            fill_exec_time_dt_val_detail = datetime.strptime(fill_exec_time_str_val_detail, '%Y%m%d  %H:%M:%S')
                                            gateway_tz = pytz.timezone('America/New_York') # Predpokladáme ET pre Gateway
                                            fill_exec_time_dt_val_detail = gateway_tz.localize(fill_exec_time_dt_val_detail, is_dst=None).astimezone(pytz.utc)
                                        except Exception as e_fill_time_detail:
                                            logger.warning(f"[{symbol}] Nepodarilo sa parsovať/konvertovať čas fillu: {fill_exec_time_str_val_detail}, chyba: {e_fill_time_detail}")

                                        entry_ts_from_data_val = inst_data.get('entry_timestamp', 0)
                                        entry_ts_dt_utc_from_data_val = datetime.fromtimestamp(entry_ts_from_data_val, tz=pytz.utc) if entry_ts_from_data_val > 0 else None

                                        if fill_exec_time_dt_val_detail and entry_ts_dt_utc_from_data_val and \
                                           fill_exec_time_dt_val_detail >= entry_ts_dt_utc_from_data_val - timedelta(seconds=config.POSITION_CONFIRM_WAIT_SECONDS * config.POSITION_CONFIRM_ATTEMPTS + 60): # Väčšia tolerancia
                                            if (current_fill_item_detail.execution.side == 'SLD' and inst_data.get('entry_signal') == 'LONG') or \
                                               (current_fill_item_detail.execution.side == 'BOT' and inst_data.get('entry_signal') == 'SHORT'):
                                                exit_price_val_close = current_fill_item_detail.execution.price
                                                logger.info(f"[{symbol}] Nájdená cena uzavretia z fills: {exit_price_val_close:{price_format_for_closing_msg}} (čas fillu: {fill_exec_time_str_val_detail})")
                                                break

                                if exit_price_val_close is None:
                                    if tf_bars_df is not None and not tf_bars_df.empty and 'close' in tf_bars_df.columns:
                                        exit_price_val_close = tf_bars_df['close'].iloc[-1]
                                        logger.warning(f"[{symbol}] Cena uzavretia z fills nebola nájdená, použijem close poslednej TF sviečky ({exit_price_val_close:{price_format_for_closing_msg}}).")
                                    else:
                                        exit_price_val_close = inst_data.get('entry_price', 0.0)
                                        logger.error(f"[{symbol}] Kritický fallback pre exit_price! Používam entry_price: {exit_price_val_close}.")

                                entry_price_for_pnl_val = inst_data.get('entry_price')
                                if not isinstance(entry_price_for_pnl_val, (int,float)) or not isinstance(exit_price_val_close, (int,float)):
                                    pnl_usd_final_value = 0.0
                                    logger.error(f"[{symbol}] Chybné typy pre entry_price ({entry_price_for_pnl_val}) alebo exit_price ({exit_price_val_close}) pre P/L. PNL=0.")
                                else:
                                    pnl_pts_final_value = (exit_price_val_close - entry_price_for_pnl_val) if inst_data.get('entry_signal') == 'LONG' else (entry_price_for_pnl_val - exit_price_val_close)
                                    pnl_usd_final_value = pnl_pts_final_value * inst_data.get('multiplier', 1) * config.QUANTITY

                                sl_price_for_check_closure = inst_data.get('sl_price')
                                if inst_data.get('trailing_set', False): closure_type_str_close = "trailing stop"
                                elif inst_data.get('sl_order_id') is None and sl_price_for_check_closure is not None and sl_price_for_check_closure != 0:
                                    if exit_price_val_close is not None:
                                        tick_s_value = config.TICK_SIZES.get(symbol, 0.01)
                                        if (inst_data.get('entry_signal') == 'LONG' and exit_price_val_close <= sl_price_for_check_closure + (tick_s_value * 0.51) ) or \
                                           (inst_data.get('entry_signal') == 'SHORT' and exit_price_val_close >= sl_price_for_check_closure - (tick_s_value * 0.51) ):
                                            closure_type_str_close = "stop-loss"

                                msg_trade_closed_final_log = f"Uzavretý {inst_data.get('entry_signal','N/A')} na {symbol} @ {exit_price_val_close:{price_format_for_closing_msg} if exit_price_val_close is not None else 'N/A'}, P/L {pnl_usd_final_value:.2f} USD (cez {closure_type_str_close})"
                                logger.info(msg_trade_closed_final_log)

                                telegram_sent_on_close = utils.send_telegram(msg_trade_closed_final_log)
                                csv_written_on_close = utils.append_trade_to_csv(
                                    symbol, inst_data.get('entry_signal','N/A'), inst_data.get('entry_price',0.0),
                                    exit_price_val_close if exit_price_val_close is not None else 0.0,
                                    pnl_usd_final_value, inst_data.get('entry_time','N/A'),
                                    inst_data.get('sl_price', 0.0), inst_data.get('trailing_set', False)
                                )
                                if telegram_sent_on_close and csv_written_on_close:
                                     last_closed_conids_session.add(closed_con_id_val)
                                     logger.info(f"[{symbol}] Uzavretie plne zalogované.")
                                else:
                                     logger.warning(f"[{symbol}] Uzavretie pre conId {closed_con_id_val} nebolo plne zalogované (TG: {telegram_sent_on_close}, CSV: {csv_written_on_close}).")
                            else:
                                logger.info(f"[{symbol}] Uzavretie pre conId {closed_con_id_val} už bolo zalogované v tejto session.")

                            inst_data.update({'inPosition': False, 'trailing_set': False, 'sl_order_id': None,
                                              'sl_price': 0.0, 'entry_signal': '', 'entry_price': 0.0,
                                              'entry_time': '', 'entry_timestamp': 0.0})
                        else:
                             logger.warning(f"[{symbol}] Falošný poplach pri uzatváraní: Pozícia je po finálnej re-kontrole stále otvorená.")
                except Exception as e_handle_closure_main_final_loop:
                    logger.error(f"[{symbol}] Chyba pri spracovaní uzavretia pozície: {e_handle_closure_main_final_loop}", exc_info=True)

        except Exception as e_main_inst_loop_very_outer_final_loop_inner_exc:
            logger.error(f"[{inst_data.get('symbol', 'NeznámySymbol')}] Neočakávaná chyba v hlavnej slučke pre inštrument: {e_main_inst_loop_very_outer_final_loop_inner_exc}", exc_info=True)
            continue


if __name__ == '__main__':
    utils.setup_logging()
    logger.info(f"Štartuje Camarilla multi-inštrument bot, TF={config.TIMEFRAME}, PID={os.getpid()}")

    bot_crashed_flag_main_outer = False
    consecutive_crashes_main_outer_loop = 0

    try:
        current_active_expiry = utils.get_front_contract_month()
    except Exception as e_init_expiry_main_outer_loop:
        logger.critical(f"Nepodarilo sa získať počiatočný mesiac expirácie: {e_init_expiry_main_outer_loop}. Ukončujem.")
        sys.exit(1)

    while True:
        is_likely_gateway_restarting_flag_outer = False
        try:
            if not ib.isConnected():
                logger.info(f"Pokus o pripojenie k IB na {config.IB_HOST}:{config.IB_PORT} s ClientID {config.IB_CLIENT_ID}")
                max_attempts_for_connection_local_outer = getattr(config, 'MAX_GATEWAY_RESTART_CONNECT_ATTEMPTS', getattr(config, 'MAX_CONNECT_ATTEMPTS', 5))

                for attempt_idx_main_conn_loop_outer_loop in range(max_attempts_for_connection_local_outer):
                    try:
                        logger.info(f"Pokus o pripojenie č. {attempt_idx_main_conn_loop_outer_loop + 1}/{max_attempts_for_connection_local_outer}...")
                        ib.connect(config.IB_HOST, config.IB_PORT, clientId=config.IB_CLIENT_ID, timeout=20)
                        if ib.isConnected():
                            logger.info("Úspešne pripojený k IB.")
                            if needs_data_initialization or not instruments_data_list or not all(inst.get('contract') for inst in instruments_data_list):
                                logger.info("Volám initialize_instruments_data() z __main__ po pripojení.")
                                initialize_instruments_data()
                            is_likely_gateway_restarting_flag_outer = False
                            break
                    except ConnectionRefusedError as cre_main_inner_loop_outer_loop:
                        logger.warning(f"Pripojenie odmietnuté (pokus {attempt_idx_main_conn_loop_outer_loop+1}): {cre_main_inner_loop_outer_loop}. IB Gateway možno ešte nebeží.")
                        is_likely_gateway_restarting_flag_outer = True
                        wait_for_gateway_s_val_outer_loop_conn_refused = 30 + (attempt_idx_main_conn_loop_outer_loop * 30)
                        wait_for_gateway_s_val_outer_loop_conn_refused = min(wait_for_gateway_s_val_outer_loop_conn_refused, 300)
                        logger.info(f"Čakám {wait_for_gateway_s_val_outer_loop_conn_refused} sekúnd, kým sa IB Gateway spustí...")
                        time.sleep(wait_for_gateway_s_val_outer_loop_conn_refused)
                        if attempt_idx_main_conn_loop_outer_loop == max_attempts_for_connection_local_outer - 1:
                            logger.error(f"Všetkých {max_attempts_for_connection_local_outer} pokusov o pripojenie po ConnectionRefused zlyhalo.")
                            raise
                    except Exception as e_conn_main_loop_inner_final_conn_outer_loop_other_exc:
                        logger.error(f"Iná chyba pri pripájaní (pokus {attempt_idx_main_conn_loop_outer_loop+1}): {e_conn_main_loop_inner_final_conn_outer_loop_other_exc}")
                        if attempt_idx_main_conn_loop_outer_loop < max_attempts_for_connection_local_outer - 1:
                            time.sleep(10 * (attempt_idx_main_conn_loop_outer_loop + 1))
                        else:
                            raise

            if not ib.isConnected():
                 logger.critical("Stále nie sme pripojení k IB po všetkých pokusoch. Ukončujem túto iteráciu vonkajšej slučky.")
                 raise ConnectionError("Finálne zlyhanie pripojenia k IB v __main__.")

            run_main_trading_loop()

            consecutive_crashes_main_outer_loop = 0
            if bot_crashed_flag_main_outer:
                utils.send_telegram("✅ Bot je znova v prevádzke po predchádzajúcom reštarte.")
                bot_crashed_flag_main_outer = False

        except ConnectionError as ce_main_loop_final_outer_conn_err_outer_loop:
            consecutive_crashes_main_outer_loop += 1
            logger.error(f"Kritická chyba pripojenia alebo pripojenie odmietnuté v hlavnom cykle: {ce_main_loop_final_outer_conn_err_outer_loop}. Pokus o reštart č. {consecutive_crashes_main_outer_loop}.")
            if not bot_crashed_flag_main_outer:
                utils.send_telegram(f"🔥 Bot má vážny problém s pripojením: {ce_main_loop_final_outer_conn_err_outer_loop}. Reštartujem.")
                bot_crashed_flag_main_outer = True
            needs_data_initialization = True
        except KeyboardInterrupt:
            logger.info("Bot bol ukončený manuálne (KeyboardInterrupt). Uvoľňujem pripojenie.")
            utils.send_telegram("🛑 Bot bol manuálne zastavený.")
            break
        except Exception as e_global_main_outermost_final_loop_exc_outer_loop_final_exc:
            consecutive_crashes_main_outer_loop += 1
            error_message_global_crash_final_log_outer = f"❌ Globálna chyba v bote: {e_global_main_outermost_final_loop_exc_outer_loop_final_exc}. Pokus o reštart č. {consecutive_crashes_main_outer_loop}."
            logger.error(error_message_global_crash_final_log_outer, exc_info=True)
            if not bot_crashed_flag_main_outer:
                utils.send_telegram(error_message_global_crash_final_log_outer)
                bot_crashed_flag_main_outer = True
            needs_data_initialization = True

        wait_duration_secs_restart_main_final_loop_val_outer_loop_final = 10 * consecutive_crashes_main_outer_loop

        if is_likely_gateway_restarting_flag_outer and consecutive_crashes_main_outer_loop <= getattr(config, 'MAX_GATEWAY_RESTART_CONNECT_ATTEMPTS', getattr(config, 'MAX_CONNECT_ATTEMPTS', 5)) :
             wait_duration_secs_restart_main_final_loop_val_outer_loop_final = 5
             is_likely_gateway_restarting_flag_outer = False

        max_consecutive_crashes_conf_outer = getattr(config, 'MAX_CONSECUTIVE_CRASHES_LIMIT', 5)
        if consecutive_crashes_main_outer_loop > max_consecutive_crashes_conf_outer :
            wait_duration_secs_restart_main_final_loop_val_outer_loop_final = 300
            logger.warning(f"Bot spadol {consecutive_crashes_main_outer_loop} krát. Pred ďalším pokusom čakám {wait_duration_secs_restart_main_final_loop_val_outer_loop_final}s.")
            if consecutive_crashes_main_outer_loop == max_consecutive_crashes_conf_outer + 1:
                 utils.send_telegram(f"🔥 Bot opakovane padá! Čakám dlhšie pred reštartom.")

        logger.info(f"Čakám {wait_duration_secs_restart_main_final_loop_val_outer_loop_final} sekúnd pred ďalším pokusom o spustenie hlavnej slučky...")

        try:
            if ib.isConnected():
                ib.disconnect()
                logger.info("Dočasne odpojený od IB pred reštartovacím cyklom.")
        except Exception as e_disc_main_outermost_final_loop_exc_outer_loop_final_disc:
            logger.error(f"Chyba pri odpájaní pred reštartom: {e_disc_main_outermost_final_loop_exc_outer_loop_final_disc}")

        time.sleep(wait_duration_secs_restart_main_final_loop_val_outer_loop_final)
        logger.info("Pokúšam sa reštartovať hlavnú slučku bota...")

    # Definitívne odpojenie pri normálnom ukončení
    if ib.isConnected():
        ib.disconnect()
    logger.info("Bot bol ukončený.")